# frozen_string_literal: true

# == Schema Information
# Table name: tenants
#
#  id         :bigint           not null, primary key
#  name       :string           not null
#  subdomain  :string           not null
#  status     :string           default("active"), not null
#  settings   :jsonb            default({}), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
# Indexes
#
#  index_tenants_on_name       (name) UNIQUE
#  index_tenants_on_settings   (settings) USING gin
#  index_tenants_on_status     (status)
#  index_tenants_on_subdomain  (subdomain) UNIQUE
#

class Tenant < ApplicationRecord
  # Associations
  has_many :users, dependent: :destroy
  has_many :campaigns, dependent: :destroy
  has_many :agent_workflows, dependent: :destroy
  
  # Vibe Marketing associations
  has_many :emotional_resonance_profiles, dependent: :destroy
  has_many :vibe_analysis_records, dependent: :destroy
  has_many :authenticity_checks, dependent: :destroy
  has_many :cultural_moments, dependent: :destroy
  has_many :ai_usage_records, dependent: :destroy
  has_many :alert_logs, dependent: :destroy

  # Validations
  validates :name, presence: true, uniqueness: true
  validates :subdomain, presence: true, uniqueness: true,
            format: {
              with: /\A[a-z0-9\-]+\z/,
              message: "must be lowercase alphanumeric with dashes only"
            }
  validates :status, presence: true, inclusion: { in: %w[active suspended cancelled] }

  # Attribute declarations for enum fields
  attribute :status, :string

  # Enums
  enum :status, { active: "active", suspended: "suspended", cancelled: "cancelled" }

  # Callbacks
  before_validation :normalize_subdomain

  # Instance methods
  def active?
    status == "active"
  end

  def full_domain(host = "localhost:3000")
    "#{subdomain}.#{host}"
  end

  private

  def normalize_subdomain
    self.subdomain = subdomain&.downcase&.strip
  end
end
