# frozen_string_literal: true

##
# AI Usage Tracker
#
# Tracks AI usage metrics, costs, and budget management for tenants.
# Provides comprehensive monitoring and alerting capabilities.
#
# @example Basic usage tracking
#   tracker = AiUsageTracker.new(tenant: current_tenant)
#   tracker.track(model: 'gpt-4o', input_tokens: 100, output_tokens: 50, duration_ms: 1500, cost: 0.005)
#
# @example Budget monitoring
#   tracker.would_exceed_budget?(0.10) # => false
#   tracker.current_usage_percentage # => 75.5
#
class AiUsageTracker
  include ActiveModel::Model

  attr_reader :tenant

  def initialize(tenant:)
    @tenant = tenant
    @redis = Redis.current
  end

  # Track AI request usage
  #
  # @param model [String] The AI model used
  # @param input_tokens [Integer] Number of input tokens
  # @param output_tokens [Integer] Number of output tokens
  # @param duration_ms [Float] Request duration in milliseconds
  # @param cost [Float] Estimated cost of the request
  # @param task_type [Symbol] Type of task performed
  def track(model:, input_tokens:, output_tokens:, duration_ms:, cost:, task_type: :general)
    usage_data = {
      tenant_id: @tenant.id,
      model: model,
      input_tokens: input_tokens,
      output_tokens: output_tokens,
      total_tokens: input_tokens + output_tokens,
      duration_ms: duration_ms,
      cost: cost,
      task_type: task_type.to_s,
      timestamp: Time.current.iso8601,
      date: Date.current.to_s
    }

    # Store detailed usage
    store_usage_record(usage_data)

    # Update aggregated metrics
    update_daily_metrics(usage_data)
    update_monthly_metrics(usage_data)

    # Check budget and alert if necessary
    check_budget_threshold(cost)

    # Log structured data for monitoring
    log_usage(usage_data)

    usage_data
  end

  # Track embedding generation usage
  #
  # @param model [String] The embedding model used
  # @param text_count [Integer] Number of texts embedded
  # @param dimensions [Integer] Embedding dimensions
  # @param cost [Float] Estimated cost
  def track_embeddings(model:, text_count:, dimensions:, cost:)
    usage_data = {
      tenant_id: @tenant.id,
      model: model,
      text_count: text_count,
      dimensions: dimensions,
      cost: cost,
      task_type: "embeddings",
      timestamp: Time.current.iso8601,
      date: Date.current.to_s
    }

    store_usage_record(usage_data)
    update_daily_metrics(usage_data)
    update_monthly_metrics(usage_data)

    log_usage(usage_data)
  end

  # Track errors for monitoring
  #
  # @param error_type [Symbol] Type of error
  # @param error_message [String] Error message
  def track_error(error_type, error_message)
    error_data = {
      tenant_id: @tenant.id,
      error_type: error_type.to_s,
      error_message: error_message,
      timestamp: Time.current.iso8601,
      date: Date.current.to_s
    }

    # Store error record
    error_key = "ai_errors:#{@tenant.id}:#{Date.current}"
    @redis.lpush(error_key, error_data.to_json)
    @redis.expire(error_key, 30.days.to_i)

    # Update error counters
    daily_error_key = "ai_error_count:#{@tenant.id}:#{Date.current}"
    @redis.incr(daily_error_key)
    @redis.expire(daily_error_key, 30.days.to_i)

    # Log error
    Rails.logger.error("AI_ERROR: #{error_data.to_json}")

    # Alert if error rate is high
    check_error_rate_threshold
  end

  # Check if a request would exceed budget
  #
  # @param additional_cost [Float] Cost of the proposed request
  # @return [Boolean] True if would exceed budget
  def would_exceed_budget?(additional_cost)
    return false unless monthly_budget_limit

    current_cost = current_monthly_cost
    (current_cost + additional_cost) > monthly_budget_limit
  end

  # Get current budget usage percentage
  #
  # @return [Float] Percentage of budget used (0-100)
  def current_usage_percentage
    return 0.0 unless monthly_budget_limit

    (current_monthly_cost / monthly_budget_limit * 100).round(2)
  end

  # Get usage summary for a date range
  #
  # @param start_date [Date] Start date for summary
  # @param end_date [Date] End date for summary
  # @return [Hash] Usage summary
  def usage_summary(start_date: 30.days.ago.to_date, end_date: Date.current)
    summary = {
      total_requests: 0,
      total_tokens: 0,
      total_cost: 0.0,
      avg_duration_ms: 0.0,
      model_breakdown: {},
      task_type_breakdown: {},
      daily_breakdown: {}
    }

    (start_date..end_date).each do |date|
      daily_data = get_daily_metrics(date)
      next if daily_data.empty?

      summary[:total_requests] += daily_data["requests"].to_i
      summary[:total_tokens] += daily_data["tokens"].to_i
      summary[:total_cost] += daily_data["cost"].to_f
      summary[:daily_breakdown][date.to_s] = daily_data

      # Aggregate model and task type data
      if daily_data["models"]
        daily_data["models"].each do |model, data|
          summary[:model_breakdown][model] ||= { requests: 0, cost: 0.0 }
          summary[:model_breakdown][model][:requests] += data["requests"].to_i
          summary[:model_breakdown][model][:cost] += data["cost"].to_f
        end
      end

      if daily_data["task_types"]
        daily_data["task_types"].each do |task_type, data|
          summary[:task_type_breakdown][task_type] ||= { requests: 0, cost: 0.0 }
          summary[:task_type_breakdown][task_type][:requests] += data["requests"].to_i
          summary[:task_type_breakdown][task_type][:cost] += data["cost"].to_f
        end
      end
    end

    # Calculate averages
    if summary[:total_requests] > 0
      total_duration = get_total_duration(start_date, end_date)
      summary[:avg_duration_ms] = (total_duration / summary[:total_requests]).round(2)
    end

    summary
  end

  # Get current monthly cost
  #
  # @return [Float] Current month's total cost
  def current_monthly_cost
    monthly_key = "ai_metrics:#{@tenant.id}:#{Date.current.beginning_of_month}"
    monthly_data = @redis.hgetall(monthly_key)
    monthly_data["cost"].to_f
  end

  # Get current daily metrics
  #
  # @return [Hash] Today's metrics
  def current_daily_metrics
    get_daily_metrics(Date.current)
  end

  # Get error rate for current day
  #
  # @return [Float] Error rate as percentage
  def current_error_rate
    total_requests = current_daily_metrics["requests"].to_i
    return 0.0 if total_requests.zero?

    error_count = daily_error_count
    (error_count.to_f / total_requests * 100).round(2)
  end

  # Reset usage data (for testing or billing cycles)
  #
  # @param period [Symbol] :daily, :monthly, or :all
  def reset_usage(period: :monthly)
    case period
    when :daily
      daily_key = "ai_metrics:#{@tenant.id}:#{Date.current}"
      @redis.del(daily_key)
    when :monthly
      start_of_month = Date.current.beginning_of_month
      end_of_month = Date.current.end_of_month

      (start_of_month..end_of_month).each do |date|
        @redis.del("ai_metrics:#{@tenant.id}:#{date}")
      end

      monthly_key = "ai_metrics:#{@tenant.id}:#{start_of_month}"
      @redis.del(monthly_key)
    when :all
      pattern = "ai_*:#{@tenant.id}:*"
      @redis.scan_each(match: pattern) do |key|
        @redis.del(key)
      end
    end
  end

  private

  # Store detailed usage record
  def store_usage_record(usage_data)
    # Store in Redis with TTL for recent access
    usage_key = "ai_usage:#{@tenant.id}:#{Date.current}"
    @redis.lpush(usage_key, usage_data.to_json)
    @redis.expire(usage_key, 90.days.to_i)

    # Also store in database for long-term analytics (async)
    StoreAiUsageJob.perform_later(usage_data)
  end

  # Update daily aggregated metrics
  def update_daily_metrics(usage_data)
    daily_key = "ai_metrics:#{@tenant.id}:#{usage_data[:date]}"

    # Increment counters
    @redis.hincrby(daily_key, "requests", 1)
    @redis.hincrby(daily_key, "tokens", usage_data[:total_tokens] || 0)
    @redis.hincrbyfloat(daily_key, "cost", usage_data[:cost])
    @redis.hincrbyfloat(daily_key, "total_duration_ms", usage_data[:duration_ms] || 0)

    # Update model-specific metrics
    if usage_data[:model]
      model_key = "models.#{usage_data[:model]}"
      @redis.hincrby(daily_key, "#{model_key}.requests", 1)
      @redis.hincrbyfloat(daily_key, "#{model_key}.cost", usage_data[:cost])
    end

    # Update task-type-specific metrics
    if usage_data[:task_type]
      task_key = "task_types.#{usage_data[:task_type]}"
      @redis.hincrby(daily_key, "#{task_key}.requests", 1)
      @redis.hincrbyfloat(daily_key, "#{task_key}.cost", usage_data[:cost])
    end

    # Set expiration
    @redis.expire(daily_key, 90.days.to_i)
  end

  # Update monthly aggregated metrics
  def update_monthly_metrics(usage_data)
    month_start = Date.parse(usage_data[:date]).beginning_of_month
    monthly_key = "ai_metrics:#{@tenant.id}:#{month_start}"

    @redis.hincrby(monthly_key, "requests", 1)
    @redis.hincrby(monthly_key, "tokens", usage_data[:total_tokens] || 0)
    @redis.hincrbyfloat(monthly_key, "cost", usage_data[:cost])

    @redis.expire(monthly_key, 1.year.to_i)
  end

  # Get daily metrics
  def get_daily_metrics(date)
    daily_key = "ai_metrics:#{@tenant.id}:#{date}"
    raw_data = @redis.hgetall(daily_key)

    return {} if raw_data.empty?

    # Parse nested data structures
    parsed_data = {}
    models = {}
    task_types = {}

    raw_data.each do |key, value|
      if key.start_with?("models.")
        # Parse model-specific data
        parts = key.split(".")
        model_name = parts[1]
        metric = parts[2]

        models[model_name] ||= {}
        models[model_name][metric] = value
      elsif key.start_with?("task_types.")
        # Parse task-type-specific data
        parts = key.split(".")
        task_type = parts[1]
        metric = parts[2]

        task_types[task_type] ||= {}
        task_types[task_type][metric] = value
      else
        parsed_data[key] = value
      end
    end

    parsed_data["models"] = models unless models.empty?
    parsed_data["task_types"] = task_types unless task_types.empty?

    parsed_data
  end

  # Get total duration for date range
  def get_total_duration(start_date, end_date)
    total = 0.0

    (start_date..end_date).each do |date|
      daily_key = "ai_metrics:#{@tenant.id}:#{date}"
      duration = @redis.hget(daily_key, "total_duration_ms").to_f
      total += duration
    end

    total
  end

  # Get daily error count
  def daily_error_count
    error_count_key = "ai_error_count:#{@tenant.id}:#{Date.current}"
    @redis.get(error_count_key).to_i
  end

  # Check budget threshold and alert if necessary
  def check_budget_threshold(additional_cost)
    return unless monthly_budget_limit

    new_cost = current_monthly_cost + additional_cost
    usage_percentage = (new_cost / monthly_budget_limit * 100)

    # Alert at 80%, 90%, and 100% thresholds
    [ 80, 90, 100 ].each do |threshold|
      if usage_percentage >= threshold && !threshold_alert_sent?(threshold)
        send_budget_alert(threshold, usage_percentage, new_cost)
        mark_threshold_alert_sent(threshold)
      end
    end
  end

  # Check error rate threshold
  def check_error_rate_threshold
    error_rate = current_error_rate

    if error_rate > 10.0 && !error_alert_sent_today?
      send_error_rate_alert(error_rate)
      mark_error_alert_sent
    end
  end

  # Check if threshold alert was already sent
  def threshold_alert_sent?(threshold)
    alert_key = "budget_alert:#{@tenant.id}:#{Date.current.beginning_of_month}:#{threshold}"
    @redis.exists?(alert_key)
  end

  # Mark threshold alert as sent
  def mark_threshold_alert_sent(threshold)
    alert_key = "budget_alert:#{@tenant.id}:#{Date.current.beginning_of_month}:#{threshold}"
    @redis.setex(alert_key, 1.month.to_i, "sent")
  end

  # Check if error alert was sent today
  def error_alert_sent_today?
    alert_key = "error_alert:#{@tenant.id}:#{Date.current}"
    @redis.exists?(alert_key)
  end

  # Mark error alert as sent
  def mark_error_alert_sent
    alert_key = "error_alert:#{@tenant.id}:#{Date.current}"
    @redis.setex(alert_key, 1.day.to_i, "sent")
  end

  # Send budget alert
  def send_budget_alert(threshold, current_percentage, current_cost)
    BudgetAlertJob.perform_later(
      tenant_id: @tenant.id,
      threshold: threshold,
      current_percentage: current_percentage,
      current_cost: current_cost,
      budget_limit: monthly_budget_limit
    )
  end

  # Send error rate alert
  def send_error_rate_alert(error_rate)
    ErrorRateAlertJob.perform_later(
      tenant_id: @tenant.id,
      error_rate: error_rate,
      date: Date.current
    )
  end

  # Get monthly budget limit from tenant settings
  def monthly_budget_limit
    @tenant.settings.dig("ai_budget", "monthly_limit")&.to_f
  end

  # Log usage with structured data
  def log_usage(usage_data)
    Rails.logger.info("AI_USAGE: #{usage_data.to_json}")

    # Send to external monitoring if configured
    if Rails.env.production? && ENV["DATADOG_API_KEY"].present?
      send_to_datadog(usage_data)
    end
  end

  # Send metrics to Datadog (example external monitoring)
  def send_to_datadog(usage_data)
    # This would integrate with Datadog or similar monitoring service
    # Implementation depends on monitoring service choice
    DatadogMetrics.increment("ai.requests", 1, tags: [
      "tenant:#{@tenant.id}",
      "model:#{usage_data[:model]}",
      "task_type:#{usage_data[:task_type]}"
    ])

    DatadogMetrics.histogram("ai.cost", usage_data[:cost], tags: [
      "tenant:#{@tenant.id}",
      "model:#{usage_data[:model]}"
    ])

    DatadogMetrics.histogram("ai.duration", usage_data[:duration_ms], tags: [
      "tenant:#{@tenant.id}",
      "model:#{usage_data[:model]}"
    ])
  rescue => e
    Rails.logger.error("Failed to send metrics to Datadog: #{e.message}")
  end
end
