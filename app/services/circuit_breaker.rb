# frozen_string_literal: true

##
# Circuit Breaker Pattern Implementation
#
# Provides resilience and fault tolerance for AI provider integrations.
# Automatically opens circuit when failure threshold is reached and
# attempts recovery after timeout period.
#
# @example Basic usage
#   breaker = CircuitBreaker.new(failure_threshold: 5, recovery_timeout: 60)
#
#   result = breaker.call do
#     # Some operation that might fail
#     external_api_call
#   end
#
# @example Provider-specific circuit breaker
#   breaker.call(provider: 'openai') do
#     openai_api_call
#   end
#
class CircuitBreaker
  include ActiveModel::Model

  # Circuit states
  CLOSED = :closed     # Normal operation
  OPEN = :open         # Circuit is open, calls fail fast
  HALF_OPEN = :half_open # Testing if service has recovered

  attr_reader :failure_threshold, :recovery_timeout, :expected_errors
  attr_accessor :failure_count, :last_failure_time, :state, :success_count_in_half_open

  def initialize(failure_threshold: 5, recovery_timeout: 60, expected_errors: [])
    @failure_threshold = failure_threshold
    @recovery_timeout = recovery_timeout
    @expected_errors = Array(expected_errors)
    @state = CLOSED
    @failure_count = 0
    @success_count_in_half_open = 0
    @last_failure_time = nil
    @redis = Redis.current
    @provider_states = {}
  end

  # Execute a block with circuit breaker protection
  #
  # @param provider [String] Optional provider name for provider-specific circuit breaking
  # @yield The block to execute
  # @return The result of the block execution
  # @raise [CircuitBreakerOpenError] When circuit is open
  def call(provider: nil)
    current_state = get_state(provider)

    case current_state
    when OPEN
      if should_attempt_reset?(provider)
        set_state(HALF_OPEN, provider)
        attempt_call(provider) { yield }
      else
        raise CircuitBreakerOpenError, "Circuit breaker is open for #{provider || 'default'}"
      end
    when HALF_OPEN
      attempt_call(provider) { yield }
    when CLOSED
      attempt_call(provider) { yield }
    end
  end

  # Check if circuit is open for a provider
  #
  # @param provider [String] Provider name
  # @return [Boolean] True if circuit is open
  def open_for?(provider)
    get_state(provider) == OPEN
  end

  # Get last error for a provider
  #
  # @param provider [String] Provider name
  # @return [String, nil] Last error message
  def last_error_for(provider)
    key = circuit_key(provider, "last_error")
    @redis.get(key)
  end

  # Get current failure count for a provider
  #
  # @param provider [String] Provider name
  # @return [Integer] Current failure count
  def failure_count_for(provider)
    key = circuit_key(provider, "failure_count")
    @redis.get(key).to_i
  end

  # Reset circuit breaker for a provider
  #
  # @param provider [String] Provider name
  def reset(provider: nil)
    set_state(CLOSED, provider)
    set_failure_count(0, provider)
    clear_last_failure_time(provider)
    clear_last_error(provider)

    Rails.logger.info("Circuit breaker reset for #{provider || 'default'}")
  end

  # Get circuit breaker status for all providers
  #
  # @return [Hash] Status information for all tracked providers
  def status
    providers = get_tracked_providers

    status_info = {
      default: {
        state: get_state(nil),
        failure_count: failure_count_for(nil),
        last_error: last_error_for(nil),
        last_failure_time: get_last_failure_time(nil)
      }
    }

    providers.each do |provider|
      status_info[provider] = {
        state: get_state(provider),
        failure_count: failure_count_for(provider),
        last_error: last_error_for(provider),
        last_failure_time: get_last_failure_time(provider)
      }
    end

    status_info
  end

  # Force circuit open for a provider (for testing or manual intervention)
  #
  # @param provider [String] Provider name
  def force_open(provider: nil)
    set_state(OPEN, provider)
    set_last_failure_time(Time.current, provider)

    Rails.logger.warn("Circuit breaker forced open for #{provider || 'default'}")
  end

  private

  # Attempt to execute the call with error handling
  def attempt_call(provider)
    start_time = Time.current

    begin
      result = yield
      on_success(provider, start_time)
      result
    rescue => error
      on_failure(provider, error, start_time)
      raise
    end
  end

  # Handle successful execution
  def on_success(provider, start_time)
    duration = (Time.current - start_time) * 1000 # Convert to milliseconds

    current_state = get_state(provider)

    case current_state
    when HALF_OPEN
      increment_success_count_in_half_open(provider)

      # If we've had enough successes, close the circuit
      if get_success_count_in_half_open(provider) >= 3
        reset(provider: provider)
        Rails.logger.info("Circuit breaker closed after successful recovery for #{provider || 'default'}")
      end
    when CLOSED
      # Reset failure count on success
      if failure_count_for(provider) > 0
        set_failure_count(0, provider)
      end
    end

    # Track success metrics
    track_success_metrics(provider, duration)
  end

  # Handle failed execution
  def on_failure(provider, error, start_time)
    duration = (Time.current - start_time) * 1000

    # Only count expected errors as circuit breaker failures
    if should_count_as_failure?(error)
      increment_failure_count(provider)
      set_last_failure_time(Time.current, provider)
      set_last_error(error.message, provider)

      current_failures = failure_count_for(provider)

      if current_failures >= @failure_threshold
        set_state(OPEN, provider)
        Rails.logger.error("Circuit breaker opened for #{provider || 'default'} after #{current_failures} failures")

        # Send alert about circuit breaker opening
        send_circuit_breaker_alert(provider, current_failures, error)
      end

      # Track failure metrics
      track_failure_metrics(provider, error, duration)
    end
  end

  # Check if we should attempt to reset the circuit
  def should_attempt_reset?(provider)
    last_failure = get_last_failure_time(provider)
    return false unless last_failure

    Time.current >= (last_failure + @recovery_timeout)
  end

  # Check if error should count as a circuit breaker failure
  def should_count_as_failure?(error)
    return true if @expected_errors.empty?

    @expected_errors.any? { |expected_error| error.is_a?(expected_error) }
  end

  # Redis key helpers
  def circuit_key(provider, suffix)
    provider_part = provider ? ":#{provider}" : ""
    "circuit_breaker#{provider_part}:#{suffix}"
  end

  # State management
  def get_state(provider)
    key = circuit_key(provider, "state")
    state_str = @redis.get(key)
    state_str ? state_str.to_sym : CLOSED
  end

  def set_state(state, provider)
    key = circuit_key(provider, "state")
    @redis.setex(key, 1.hour.to_i, state.to_s)
  end

  # Failure count management
  def increment_failure_count(provider)
    key = circuit_key(provider, "failure_count")
    @redis.incr(key)
    @redis.expire(key, 1.hour.to_i)
  end

  def set_failure_count(count, provider)
    key = circuit_key(provider, "failure_count")
    if count.zero?
      @redis.del(key)
    else
      @redis.setex(key, 1.hour.to_i, count)
    end
  end

  # Success count in half-open state
  def increment_success_count_in_half_open(provider)
    key = circuit_key(provider, "half_open_success_count")
    @redis.incr(key)
    @redis.expire(key, 10.minutes.to_i)
  end

  def get_success_count_in_half_open(provider)
    key = circuit_key(provider, "half_open_success_count")
    @redis.get(key).to_i
  end

  # Last failure time management
  def set_last_failure_time(time, provider)
    key = circuit_key(provider, "last_failure_time")
    @redis.setex(key, 1.hour.to_i, time.to_f)
  end

  def get_last_failure_time(provider)
    key = circuit_key(provider, "last_failure_time")
    timestamp = @redis.get(key)
    timestamp ? Time.at(timestamp.to_f) : nil
  end

  def clear_last_failure_time(provider)
    key = circuit_key(provider, "last_failure_time")
    @redis.del(key)
  end

  # Last error management
  def set_last_error(error_message, provider)
    key = circuit_key(provider, "last_error")
    @redis.setex(key, 1.hour.to_i, error_message)
  end

  def clear_last_error(provider)
    key = circuit_key(provider, "last_error")
    @redis.del(key)
  end

  # Get list of tracked providers
  def get_tracked_providers
    pattern = "circuit_breaker:*:state"
    providers = []

    @redis.scan_each(match: pattern) do |key|
      # Extract provider name from key
      parts = key.split(":")
      if parts.length == 3 # circuit_breaker:provider:state
        providers << parts[1]
      end
    end

    providers.uniq
  end

  # Track success metrics
  def track_success_metrics(provider, duration)
    metrics_data = {
      provider: provider || "default",
      status: "success",
      duration_ms: duration,
      timestamp: Time.current.iso8601
    }

    Rails.logger.info("CIRCUIT_BREAKER_SUCCESS: #{metrics_data.to_json}")

    # Store metrics for monitoring
    store_metrics(provider, "success", duration)
  end

  # Track failure metrics
  def track_failure_metrics(provider, error, duration)
    metrics_data = {
      provider: provider || "default",
      status: "failure",
      error_class: error.class.name,
      error_message: error.message,
      duration_ms: duration,
      timestamp: Time.current.iso8601
    }

    Rails.logger.error("CIRCUIT_BREAKER_FAILURE: #{metrics_data.to_json}")

    # Store metrics for monitoring
    store_metrics(provider, "failure", duration, error)
  end

  # Store metrics in Redis for monitoring
  def store_metrics(provider, status, duration, error = nil)
    date = Date.current.to_s
    metrics_key = "circuit_breaker_metrics:#{provider || 'default'}:#{date}"

    # Increment counters
    @redis.hincrby(metrics_key, "#{status}_count", 1)
    @redis.hincrbyfloat(metrics_key, "#{status}_total_duration", duration)

    if error
      @redis.hincrby(metrics_key, "error_#{error.class.name.underscore}", 1)
    end

    # Set expiration
    @redis.expire(metrics_key, 30.days.to_i)
  end

  # Send alert when circuit breaker opens
  def send_circuit_breaker_alert(provider, failure_count, error)
    CircuitBreakerAlertJob.perform_later(
      provider: provider || "default",
      failure_count: failure_count,
      error_message: error.message,
      error_class: error.class.name,
      timestamp: Time.current
    )
  end

  # Custom exception for when circuit is open
  class CircuitBreakerOpenError < StandardError; end
end
