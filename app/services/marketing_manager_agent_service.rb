# frozen_string_literal: true

# Marketing Manager Agent - Orchestrates specialist AI agents for automated campaign management
class MarketingManagerAgentService
  include ActiveModel::Model
  include ActiveModel::Validations

  attr_reader :campaign, :tenant, :workflow, :ai_service, :context

  def initialize(campaign:, context: {})
    @campaign = campaign
    @tenant = campaign.tenant
    @context = context
    @ai_service = OpenAiService.new
    @specialist_agents = load_specialist_agents
  end

  # Main orchestration method - coordinates the entire AI workflow
  def orchestrate_campaign_workflow
    return false unless valid_for_orchestration?

    @workflow = create_workflow

    begin
      execute_workflow_phases
      @workflow.mark_as_completed!(generate_completion_summary)
      true
    rescue => error
      handle_workflow_error(error)
      false
    end
  end

  # Generate campaign content across all channels
  def generate_multi_channel_content
    return false unless valid_for_content_generation?

    @workflow = create_workflow(workflow_type: :campaign_generation)

    begin
      @workflow.mark_as_started!

      # Phase 1: Analyze campaign requirements
      analyze_campaign_requirements

      # Phase 2: Generate content for each channel
      generate_channel_content

      # Phase 3: Optimize and coordinate across channels
      optimize_cross_channel_messaging

      @workflow.mark_as_completed!(content_generation_summary)
      true
    rescue => error
      handle_workflow_error(error)
      false
    end
  end

  # Analyze campaign performance and provide optimization recommendations
  def analyze_and_optimize_performance
    return false unless campaign.active? || campaign.completed?

    @workflow = create_workflow(workflow_type: :performance_analysis)

    begin
      @workflow.mark_as_started!

      # Gather performance data
      performance_data = collect_performance_metrics

      # Analyze with AI
      analysis_results = @ai_service.analyze_performance(
        campaign_data: campaign_analysis_context,
        metrics: performance_data,
        goals: campaign.settings.dig("goals") || {}
      )

      # Generate actionable recommendations
      recommendations = generate_optimization_recommendations(analysis_results)

      @workflow.mark_as_completed!({
        analysis: analysis_results,
        recommendations: recommendations,
        performance_data: performance_data
      })

      true
    rescue => error
      handle_workflow_error(error)
      false
    end
  end

  private

  def valid_for_orchestration?
    campaign.present? && tenant.present?
  end

  def valid_for_content_generation?
    valid_for_orchestration? && campaign.draft?
  end

  def create_workflow(workflow_type: :multi_channel_coordination)
    AgentWorkflow.create!(
      campaign: campaign,
      tenant: tenant,
      workflow_type: workflow_type,
      context_data: initial_context_data,
      total_steps: calculate_total_steps(workflow_type)
    )
  end

  def load_specialist_agents
    {
      email: EmailSpecialistAgentService.new(campaign: campaign, ai_service: @ai_service),
      social: SocialMediaAgentService.new(campaign: campaign, ai_service: @ai_service),
      seo: SeoSpecialistAgentService.new(campaign: campaign, ai_service: @ai_service)
    }
  end

  def execute_workflow_phases
    @workflow.mark_as_started!

    # Phase 1: Campaign Analysis (20%)
    analyze_campaign_requirements
    @workflow.update_progress!("analysis_complete", 20)

    # Phase 2: Content Generation (60%)
    if campaign_needs_content_generation?
      generate_channel_content
      @workflow.update_progress!("content_generated", 60)
    end

    # Phase 3: Cross-Channel Optimization (80%)
    optimize_cross_channel_messaging
    @workflow.update_progress!("optimization_complete", 80)

    # Phase 4: Performance Setup (100%)
    setup_performance_tracking
    @workflow.update_progress!("workflow_complete", 100)
  end

  def analyze_campaign_requirements
    analysis_context = {
      campaign_name: campaign.name,
      campaign_type: campaign.campaign_type,
      target_audience: campaign.target_audience,
      budget: campaign.budget_in_dollars,
      timeline: {
        start_date: campaign.start_date,
        end_date: campaign.end_date,
        duration_days: campaign.duration_in_days
      },
      settings: campaign.settings
    }

    @workflow.add_context("campaign_analysis", analysis_context)

    # Store analysis for specialist agents
    @context[:campaign_analysis] = analysis_context
  end

  def campaign_needs_content_generation?
    case campaign.campaign_type
    when "email"
      campaign.email_campaign.blank? || campaign.email_campaign.content.blank?
    when "social"
      campaign.social_campaign.blank? || campaign.social_campaign.content_variants.blank?
    when "seo"
      campaign.seo_campaign.blank? || campaign.seo_campaign.content_strategy.blank?
    when "multi_channel"
      true # Multi-channel always needs coordination
    else
      false
    end
  end

  def generate_channel_content
    case campaign.campaign_type
    when "email"
      delegate_to_email_agent
    when "social"
      delegate_to_social_agent
    when "seo"
      delegate_to_seo_agent
    when "multi_channel"
      delegate_to_all_agents
    end
  end

  def delegate_to_email_agent
    result = @specialist_agents[:email].generate_campaign_content(@context)
    @workflow.add_context("email_results", result)
  end

  def delegate_to_social_agent
    result = @specialist_agents[:social].generate_campaign_content(@context)
    @workflow.add_context("social_results", result)
  end

  def delegate_to_seo_agent
    result = @specialist_agents[:seo].generate_campaign_content(@context)
    @workflow.add_context("seo_results", result)
  end

  def delegate_to_all_agents
    email_result = @specialist_agents[:email].generate_campaign_content(@context)
    social_result = @specialist_agents[:social].generate_campaign_content(@context)
    seo_result = @specialist_agents[:seo].generate_campaign_content(@context)

    @workflow.add_context("multi_channel_results", {
      email: email_result,
      social: social_result,
      seo: seo_result
    })
  end

  def optimize_cross_channel_messaging
    # Ensure messaging consistency across channels
    optimization_prompt = build_optimization_prompt

    optimization_results = @ai_service.generate_content(
      prompt: optimization_prompt,
      temperature: 0.6
    )

    @workflow.add_context("optimization_results", optimization_results)
    apply_optimization_recommendations(optimization_results)
  end

  def build_optimization_prompt
    channel_content = gather_generated_content

    <<~PROMPT
      Review and optimize this multi-channel marketing campaign for consistency and effectiveness:

      Campaign: #{campaign.name}
      Target Audience: #{campaign.target_audience}

      Generated Content:
      #{channel_content.to_json}

      Please provide:
      1. Messaging consistency analysis
      2. Cross-channel optimization recommendations
      3. Specific improvements for each channel
      4. Overall campaign coherence score (1-10)

      Format as JSON with clear recommendations.
    PROMPT
  end

  def gather_generated_content
    {
      email: @workflow.get_context("email_results"),
      social: @workflow.get_context("social_results"),
      seo: @workflow.get_context("seo_results")
    }.compact
  end

  def apply_optimization_recommendations(optimization_results)
    # Apply AI-recommended optimizations to generated content
    # This would update the associated campaign types based on AI feedback
    Rails.logger.info "Optimization recommendations: #{optimization_results}"
  end

  def setup_performance_tracking
    # Initialize performance tracking for the campaign
    initial_metrics = {
      tracking_setup_at: Time.current,
      baseline_metrics: collect_baseline_metrics,
      optimization_goals: extract_optimization_goals
    }

    @workflow.add_context("performance_setup", initial_metrics)
  end

  def collect_performance_metrics
    campaign.campaign_metrics.recent(30).includes(:campaign).map do |metric|
      {
        date: metric.metric_date,
        impressions: metric.impressions,
        clicks: metric.clicks,
        conversions: metric.conversions,
        revenue: metric.revenue_cents / 100.0,
        cost: metric.cost_cents / 100.0
      }
    end
  end

  def collect_baseline_metrics
    # Collect current baseline metrics for comparison
    {
      current_ctr: campaign.click_through_rate,
      current_conversion_rate: campaign.conversion_rate,
      current_roas: campaign.return_on_ad_spend,
      total_budget: campaign.budget_in_dollars
    }
  end

  def extract_optimization_goals
    campaign.settings.dig("optimization_goals") || {
      target_ctr: 3.0,
      target_conversion_rate: 2.5,
      target_roas: 400.0
    }
  end

  def campaign_analysis_context
    {
      name: campaign.name,
      type: campaign.campaign_type,
      audience: campaign.target_audience,
      budget: campaign.budget_in_dollars,
      duration: campaign.duration_in_days,
      settings: campaign.settings
    }
  end

  def generate_optimization_recommendations(analysis_results)
    # Convert AI analysis into actionable recommendations
    base_recommendations = [
      "Review and optimize ad targeting based on performance data",
      "Adjust budget allocation across high-performing channels",
      "Test alternative messaging variants",
      "Optimize posting/send times based on audience engagement patterns"
    ]

    if analysis_results.is_a?(Hash) && analysis_results["recommendations"]
      base_recommendations + Array(analysis_results["recommendations"])
    else
      base_recommendations
    end
  end

  def calculate_total_steps(workflow_type)
    case workflow_type
    when :campaign_generation
      4 # analysis, generation, optimization, finalization
    when :performance_analysis
      3 # data collection, analysis, recommendations
    when :multi_channel_coordination
      4 # analysis, content generation, optimization, tracking setup
    else
      3
    end
  end

  def initial_context_data
    {
      orchestrator: "MarketingManagerAgent",
      campaign_id: campaign.id,
      tenant_id: tenant.id,
      initiated_at: Time.current,
      user_context: @context
    }
  end

  def content_generation_summary
    {
      workflow_type: "campaign_generation",
      channels_processed: determine_processed_channels,
      content_generated: @workflow.context_data.keys.select { |k| k.include?("_results") },
      optimization_applied: @workflow.get_context("optimization_results").present?,
      ready_for_activation: campaign_ready_for_activation?
    }
  end

  def generate_completion_summary
    {
      workflow_type: @workflow.workflow_type,
      total_duration: @workflow.duration,
      phases_completed: @workflow.context_data.keys.count,
      specialist_agents_used: @specialist_agents.keys,
      final_status: "completed_successfully"
    }
  end

  def determine_processed_channels
    channels = []
    channels << "email" if @workflow.get_context("email_results")
    channels << "social" if @workflow.get_context("social_results")
    channels << "seo" if @workflow.get_context("seo_results")
    channels
  end

  def campaign_ready_for_activation?
    case campaign.campaign_type
    when "email"
      campaign.email_campaign&.content.present?
    when "social"
      campaign.social_campaign&.content_variants&.any?
    when "seo"
      campaign.seo_campaign&.content_strategy&.any?
    when "multi_channel"
      campaign.email_campaign&.content.present? ||
        campaign.social_campaign&.content_variants&.any? ||
        campaign.seo_campaign&.content_strategy&.any?
    else
      false
    end
  end

  def handle_workflow_error(error)
    error_message = "Marketing Manager Agent workflow failed: #{error.message}"
    Rails.logger.error error_message
    Rails.logger.error error.backtrace.join("\n")

    @workflow&.mark_as_failed!(error_message, {
      error_class: error.class.name,
      backtrace: error.backtrace&.first(5),
      context_at_failure: @workflow&.context_data
    })
  end
end
