# frozen_string_literal: true

require "net/http"
require "json"

# OpenAI API integration service for AI agent workflows
class OpenAiService
  class OpenAiError < StandardError; end
  class RateLimitError < OpenAiError; end
  class InvalidRequestError < OpenAiError; end

  API_BASE_URL = "https://api.openai.com/v1"
  DEFAULT_MODEL = "gpt-4o"
  DEFAULT_TEMPERATURE = 0.7
  DEFAULT_MAX_TOKENS = 2000

  def initialize(api_key: nil)
    @api_key = api_key || Rails.application.credentials.openai_api_key || ENV["OPENAI_API_KEY"]
    raise OpenAiError, "OpenAI API key not configured" unless @api_key
  end

  # Generate marketing content using GPT-4o
  def generate_content(prompt:, model: DEFAULT_MODEL, temperature: DEFAULT_TEMPERATURE, max_tokens: DEFAULT_MAX_TOKENS)
    response = make_request(
      endpoint: "/chat/completions",
      payload: {
        model: model,
        messages: [
          {
            role: "system",
            content: "You are an expert marketing strategist and copywriter specializing in AI-driven campaigns."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: temperature,
        max_tokens: max_tokens
      }
    )

    extract_content_from_response(response)
  end

  # Generate email campaign content
  def generate_email_campaign(campaign_name:, target_audience:, campaign_goals:, brand_voice: "professional")
    prompt = build_email_prompt(
      campaign_name: campaign_name,
      target_audience: target_audience,
      campaign_goals: campaign_goals,
      brand_voice: brand_voice
    )

    response = generate_content(prompt: prompt, temperature: 0.8)
    parse_email_response(response)
  end

  # Generate social media content
  def generate_social_content(campaign_name:, platforms:, target_audience:, key_message:)
    prompt = build_social_prompt(
      campaign_name: campaign_name,
      platforms: platforms,
      target_audience: target_audience,
      key_message: key_message
    )

    response = generate_content(prompt: prompt, temperature: 0.9)
    parse_social_response(response, platforms)
  end

  # Generate SEO content strategy
  def generate_seo_strategy(campaign_name:, target_keywords:, target_audience:, business_type:)
    prompt = build_seo_prompt(
      campaign_name: campaign_name,
      target_keywords: target_keywords,
      target_audience: target_audience,
      business_type: business_type
    )

    response = generate_content(prompt: prompt, temperature: 0.6)
    parse_seo_response(response)
  end

  # Analyze campaign performance and provide recommendations
  def analyze_performance(campaign_data:, metrics:, goals:)
    prompt = build_analysis_prompt(
      campaign_data: campaign_data,
      metrics: metrics,
      goals: goals
    )

    response = generate_content(prompt: prompt, temperature: 0.5)
    parse_analysis_response(response)
  end

  private

  def make_request(endpoint:, payload:)
    uri = URI("#{API_BASE_URL}#{endpoint}")
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = true
    http.read_timeout = 60

    request = Net::HTTP::Post.new(uri)
    request["Content-Type"] = "application/json"
    request["Authorization"] = "Bearer #{@api_key}"
    request.body = payload.to_json

    response = http.request(request)
    handle_response(response)
  end

  def handle_response(response)
    case response.code.to_i
    when 200..299
      JSON.parse(response.body)
    when 429
      raise RateLimitError, "OpenAI API rate limit exceeded"
    when 400..499
      error_data = JSON.parse(response.body) rescue {}
      raise InvalidRequestError, error_data.dig("error", "message") || "Invalid request"
    else
      raise OpenAiError, "OpenAI API error: #{response.code} - #{response.body}"
    end
  rescue JSON::ParserError
    raise OpenAiError, "Invalid JSON response from OpenAI API"
  end

  def extract_content_from_response(response)
    response.dig("choices", 0, "message", "content")&.strip
  end

  def build_email_prompt(campaign_name:, target_audience:, campaign_goals:, brand_voice:)
    <<~PROMPT
      Create a comprehensive email marketing campaign for: #{campaign_name}

      Target Audience: #{target_audience}
      Campaign Goals: #{campaign_goals}
      Brand Voice: #{brand_voice}

      Please provide:
      1. Subject Line (compelling, 50 characters or less)
      2. Preview Text (engaging, under 90 characters)
      3. Email Content (HTML-friendly, structured with clear sections)
      4. Call-to-Action (specific, action-oriented)

      Format your response as JSON with keys: subject_line, preview_text, content, cta
      Ensure the content is personalized, engaging, and optimized for conversions.
    PROMPT
  end

  def build_social_prompt(campaign_name:, platforms:, target_audience:, key_message:)
    platforms_list = platforms.join(", ")

    <<~PROMPT
      Create social media content for: #{campaign_name}

      Platforms: #{platforms_list}
      Target Audience: #{target_audience}
      Key Message: #{key_message}

      For each platform, provide:
      1. Post content (optimized for platform character limits)
      2. Hashtags (relevant and trending)
      3. Best posting time recommendations
      4. Engagement strategies

      Format as JSON with platform names as keys.
      Consider platform-specific best practices and audience behavior.
    PROMPT
  end

  def build_seo_prompt(campaign_name:, target_keywords:, target_audience:, business_type:)
    keywords_list = Array(target_keywords).join(", ")

    <<~PROMPT
      Create an SEO content strategy for: #{campaign_name}

      Target Keywords: #{keywords_list}
      Target Audience: #{target_audience}
      Business Type: #{business_type}

      Provide:
      1. Meta title (60 characters max, keyword-optimized)
      2. Meta description (160 characters max, compelling)
      3. Content outline (H1, H2, H3 structure)
      4. Target pages suggestions
      5. Content calendar (3-month plan)
      6. Keyword variations and LSI keywords

      Format as JSON with clear structure for implementation.
      Focus on search intent and user value.
    PROMPT
  end

  def build_analysis_prompt(campaign_data:, metrics:, goals:)
    <<~PROMPT
      Analyze this marketing campaign performance:

      Campaign Data: #{campaign_data.to_json}
      Current Metrics: #{metrics.to_json}
      Original Goals: #{goals.to_json}

      Provide:
      1. Performance summary (key wins and gaps)
      2. Goal achievement analysis
      3. Actionable recommendations for improvement
      4. Optimization opportunities
      5. Next steps prioritized by impact

      Format as JSON with clear sections.
      Be specific and data-driven in recommendations.
    PROMPT
  end

  def parse_email_response(response)
    # Try to parse as JSON first, fallback to text parsing
    begin
      JSON.parse(response)
    rescue JSON::ParserError
      # Fallback parsing for non-JSON responses
      extract_email_components_from_text(response)
    end
  end

  def parse_social_response(response, platforms)
    begin
      JSON.parse(response)
    rescue JSON::ParserError
      # Create structured response from text
      platforms.each_with_object({}) do |platform, result|
        result[platform] = {
          content: response.split("\n").first || response[0..200],
          hashtags: extract_hashtags_from_text(response),
          posting_time: "Peak engagement hours for #{platform}",
          engagement_strategy: "Platform-specific engagement tactics"
        }
      end
    end
  end

  def parse_seo_response(response)
    begin
      JSON.parse(response)
    rescue JSON::ParserError
      extract_seo_components_from_text(response)
    end
  end

  def parse_analysis_response(response)
    begin
      JSON.parse(response)
    rescue JSON::ParserError
      {
        summary: response.split("\n").first || response[0..200],
        recommendations: extract_recommendations_from_text(response),
        next_steps: [ "Review generated analysis", "Implement top recommendations" ]
      }
    end
  end

  # Fallback text parsing methods
  def extract_email_components_from_text(text)
    {
      subject_line: text.lines.find { |line| line.downcase.include?("subject") }&.strip || "Generated Email Campaign",
      preview_text: "Engaging preview for your campaign",
      content: text,
      cta: "Take Action Now"
    }
  end

  def extract_hashtags_from_text(text)
    text.scan(/#\w+/).join(" ")
  end

  def extract_seo_components_from_text(text)
    {
      meta_title: text.lines.first&.strip || "SEO-Optimized Title",
      meta_description: text.lines[1]&.strip || "Compelling meta description for search results",
      content_outline: text,
      keyword_variations: []
    }
  end

  def extract_recommendations_from_text(text)
    text.split(/\d+\.|\n-|\n\*/).map(&:strip).reject(&:empty?)[1..5] || []
  end
end
