# frozen_string_literal: true

##
# Circuit Breaker Alert Job
#
# Sends alerts when circuit breakers open due to provider failures.
# Critical for maintaining system reliability awareness.
#
class CircuitBreakerAlertJob < ApplicationJob
  queue_as :critical

  retry_on StandardError, wait: :exponentially_longer, attempts: 2

  def perform(provider:, failure_count:, error_message:, error_class:, timestamp:)
    alert_data = {
      provider: provider,
      failure_count: failure_count,
      error_message: error_message,
      error_class: error_class,
      timestamp: timestamp
    }

    # Send notifications to system administrators
    send_admin_email_alert(alert_data)
    send_ops_slack_alert(alert_data) if ops_slack_configured?
    send_webhook_alert(alert_data) if monitoring_webhook_configured?

    # Log critical system alert
    log_circuit_breaker_alert(alert_data)

    Rails.logger.error("Circuit breaker opened for provider #{provider} after #{failure_count} failures")
  rescue => e
    Rails.logger.error("Failed to send circuit breaker alert: #{e.message}")
    raise
  end

  private

  def send_admin_email_alert(alert_data)
    # Send to system administrators
    admin_emails = ENV["SYSTEM_ADMIN_EMAILS"]&.split(",") || []

    admin_emails.each do |email|
      CircuitBreakerAlertMailer.circuit_opened(
        email: email.strip,
        provider: alert_data[:provider],
        failure_count: alert_data[:failure_count],
        error_message: alert_data[:error_message],
        error_class: alert_data[:error_class],
        timestamp: alert_data[:timestamp]
      ).deliver_now
    end
  end

  def send_ops_slack_alert(alert_data)
    slack_webhook_url = ENV["OPS_SLACK_WEBHOOK_URL"]

    message = build_ops_slack_message(alert_data)

    Faraday.post(slack_webhook_url) do |req|
      req.headers["Content-Type"] = "application/json"
      req.body = {
        text: message,
        channel: "#alerts",
        username: "AI-Marketing-System",
        icon_emoji: ":warning:"
      }.to_json
    end
  end

  def send_webhook_alert(alert_data)
    webhook_url = ENV["MONITORING_WEBHOOK_URL"]

    payload = {
      event: "circuit_breaker_opened",
      provider: alert_data[:provider],
      failure_count: alert_data[:failure_count],
      error_message: alert_data[:error_message],
      error_class: alert_data[:error_class],
      timestamp: alert_data[:timestamp].iso8601,
      severity: "critical",
      system: "ai-marketing-platform"
    }

    Faraday.post(webhook_url) do |req|
      req.headers["Content-Type"] = "application/json"
      req.headers["Authorization"] = "Bearer #{ENV['MONITORING_WEBHOOK_TOKEN']}" if ENV["MONITORING_WEBHOOK_TOKEN"]
      req.body = payload.to_json
    end
  end

  def build_ops_slack_message(alert_data)
    provider = alert_data[:provider]
    failure_count = alert_data[:failure_count]
    error_class = alert_data[:error_class]
    error_message = alert_data[:error_message]
    timestamp = alert_data[:timestamp]

    <<~MESSAGE
      🚨 **CRITICAL: Circuit Breaker Opened**

      **Provider:** #{provider}
      **Failures:** #{failure_count} consecutive failures
      **Error Type:** #{error_class}
      **Error:** #{error_message.truncate(200)}
      **Time:** #{timestamp.strftime('%Y-%m-%d %H:%M:%S %Z')}

      **Impact:** All requests to #{provider} are now failing fast.
      **Action Required:** Investigate provider status and system health.

      The circuit will automatically attempt recovery after the timeout period.
    MESSAGE
  end

  def log_circuit_breaker_alert(alert_data)
    # Log to system alert log (could be a separate model for system-wide alerts)
    Rails.logger.error("CIRCUIT_BREAKER_ALERT: #{alert_data.to_json}")

    # Store in Redis for monitoring dashboard
    redis = Redis.current
    alert_key = "system_alerts:circuit_breaker:#{Date.current}"

    alert_record = {
      provider: alert_data[:provider],
      failure_count: alert_data[:failure_count],
      error_class: alert_data[:error_class],
      error_message: alert_data[:error_message],
      timestamp: alert_data[:timestamp].iso8601
    }

    redis.lpush(alert_key, alert_record.to_json)
    redis.expire(alert_key, 30.days.to_i)

    # Increment system-wide alert counter
    daily_counter_key = "system_alert_count:#{Date.current}"
    redis.incr(daily_counter_key)
    redis.expire(daily_counter_key, 30.days.to_i)
  end

  def ops_slack_configured?
    ENV["OPS_SLACK_WEBHOOK_URL"].present?
  end

  def monitoring_webhook_configured?
    ENV["MONITORING_WEBHOOK_URL"].present?
  end
end
