<!DOCTYPE html>
<html>
  <head>
    <title>AI Marketing Hub<%= " - #{yield(:title)}" if content_for?(:title) %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
    
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- Shared Navigation -->
    <%= render 'shared/navbar' %>

    <!-- Flash Messages -->
    <%= render 'shared/flash_messages' %>

    <% if user_signed_in? %>
      <!-- Mobile Sidebar Overlay -->
      <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden"></div>

      <!-- Main Layout with Sidebar -->
      <div class="flex h-screen pt-16">
        <!-- Enhanced Sophisticated Sidebar -->
        <nav id="sidebar" class="fixed lg:static inset-y-0 left-0 z-50 w-64 lg:w-20 xl:w-64 bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 shadow-2xl border-r border-slate-700/50 transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out backdrop-blur-xl">
          <div class="flex flex-col h-full pt-16 lg:pt-0">
            <!-- Sidebar Header (Mobile Only) -->
            <div class="flex items-center justify-between p-4 border-b border-slate-700/50 lg:hidden">
              <h2 class="text-lg font-semibold text-white">Navigation</h2>
              <button id="sidebar-close" class="p-2 rounded-lg text-slate-400 hover:text-white hover:bg-slate-700/50 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <!-- Brand Logo/Title (Compact Mode) -->
            <div class="px-4 py-6 border-b border-slate-700/30 lg:block xl:hidden">
              <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
            </div>

            <!-- Navigation Links -->
            <div class="flex-1 px-4 py-6 space-y-1 overflow-y-auto">
              <!-- Dashboard -->
              <%= link_to dashboard_path, class: "group relative flex items-center px-3 py-3 rounded-xl transition-all duration-200 #{ current_page?(dashboard_path) ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg' : 'text-slate-300 hover:bg-slate-700/50 hover:text-white' }", data: { nav: "dashboard" } do %>
                <svg class="w-6 h-6 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                </svg>
                <span class="ml-3 font-medium lg:hidden xl:block transition-all">Dashboard</span>
                <!-- Enhanced Tooltip for compact mode -->
                <div class="nav-tooltip lg:block xl:hidden absolute left-full ml-3 px-3 py-2 bg-slate-900 text-white text-sm rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50 whitespace-nowrap">
                  Dashboard
                  <div class="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-slate-900 rotate-45"></div>
                </div>
              <% end %>

              <!-- Campaigns -->
              <%= link_to campaigns_path, class: "group relative flex items-center px-3 py-3 rounded-xl transition-all duration-200 #{ current_page?(campaigns_path) || controller_name == 'campaigns' ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg' : 'text-slate-300 hover:bg-slate-700/50 hover:text-white' }", data: { nav: "campaigns" } do %>
                <svg class="w-6 h-6 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                </svg>
                <span class="ml-3 font-medium lg:hidden xl:block transition-all">Campaigns</span>
                <div class="nav-tooltip lg:block xl:hidden absolute left-full ml-3 px-3 py-2 bg-slate-900 text-white text-sm rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50 whitespace-nowrap">
                  Campaigns
                  <div class="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-slate-900 rotate-45"></div>
                </div>
              <% end %>

              <!-- Vibe Analytics -->
              <div class="group relative flex items-center px-3 py-3 rounded-xl transition-all duration-200 text-slate-300 hover:bg-slate-700/50 hover:text-white cursor-pointer" data-nav="vibe">
                <svg class="w-6 h-6 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                  <circle cx="12" cy="12" r="2" fill="currentColor"/>
                </svg>
                <span class="ml-3 font-medium lg:hidden xl:block">Vibe Analytics</span>
                <span class="ml-auto text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2 py-1 rounded-full lg:hidden xl:block animate-pulse">New</span>
                <div class="nav-tooltip lg:block xl:hidden absolute left-full ml-3 px-3 py-2 bg-slate-900 text-white text-sm rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50 whitespace-nowrap">
                  Vibe Analytics
                  <div class="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-slate-900 rotate-45"></div>
                </div>
              </div>

              <!-- Audiences -->
              <div class="group relative flex items-center px-3 py-3 rounded-xl transition-all duration-200 text-slate-500 cursor-not-allowed">
                <svg class="w-6 h-6 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                </svg>
                <span class="ml-3 font-medium lg:hidden xl:block">Audiences</span>
                <span class="ml-auto text-xs bg-slate-600 text-slate-400 px-2 py-1 rounded-full lg:hidden xl:block">Soon</span>
                <div class="nav-tooltip lg:block xl:hidden absolute left-full ml-3 px-3 py-2 bg-slate-900 text-white text-sm rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50 whitespace-nowrap">
                  Audiences (Coming Soon)
                  <div class="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-slate-900 rotate-45"></div>
                </div>
              </div>

              <!-- Content Library -->
              <div class="group relative flex items-center px-3 py-3 rounded-xl transition-all duration-200 text-slate-500 cursor-not-allowed">
                <svg class="w-6 h-6 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6"/>
                </svg>
                <span class="ml-3 font-medium lg:hidden xl:block">Content Library</span>
                <span class="ml-auto text-xs bg-slate-600 text-slate-400 px-2 py-1 rounded-full lg:hidden xl:block">Soon</span>
                <div class="nav-tooltip lg:block xl:hidden absolute left-full ml-3 px-3 py-2 bg-slate-900 text-white text-sm rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50 whitespace-nowrap">
                  Content Library (Coming Soon)
                  <div class="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-slate-900 rotate-45"></div>
                </div>
              </div>

              <!-- Section Divider -->
              <div class="py-3">
                <div class="border-t border-slate-700/30"></div>
              </div>

              <!-- Settings -->
              <div class="group relative flex items-center px-3 py-3 rounded-xl transition-all duration-200 text-slate-300 hover:bg-slate-700/50 hover:text-white cursor-pointer">
                <svg class="w-6 h-6 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                  <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
                <span class="ml-3 font-medium lg:hidden xl:block">Settings</span>
                <div class="nav-tooltip lg:block xl:hidden absolute left-full ml-3 px-3 py-2 bg-slate-900 text-white text-sm rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50 whitespace-nowrap">
                  Settings
                  <div class="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-slate-900 rotate-45"></div>
                </div>
              </div>
            </div>

            <!-- Enhanced Sidebar Footer -->
            <div class="p-4 border-t border-slate-700/30">
              <div class="flex items-center group">
                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg">
                  <span class="text-white text-sm font-semibold">
                    <%= current_user.email.first.upcase %>
                  </span>
                </div>
                <div class="ml-3 lg:hidden xl:block">
                  <p class="text-sm font-medium text-white truncate">
                    <%= current_user.email.split('@').first.titleize %>
                  </p>
                  <p class="text-xs text-slate-400">
                    <%= current_user.tenant.name if current_user.tenant %>
                  </p>
                </div>
                <!-- Logout Button -->
                <div class="ml-auto lg:hidden xl:block">
                  <%= link_to destroy_user_session_path, method: :delete, 
                      class: "p-2 text-slate-400 hover:text-white hover:bg-slate-700/50 rounded-lg transition-colors",
                      title: "Sign Out" do %>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                    </svg>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        </nav>

        <!-- Main Content Area -->
        <main class="flex-1 lg:ml-20 xl:ml-64 min-h-screen bg-gray-50">
          <!-- Content Container with Responsive Padding -->
          <div class="h-full overflow-auto">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8">
              <%= yield %>
            </div>
          </div>
        </main>
      </div>
    <% else %>
      <!-- Non-authenticated layout -->
      <main class="pt-16 min-h-screen">
        <div class="h-full overflow-auto">
          <%= yield %>
        </div>
      </main>
    <% end %>
    
    <!-- Shared Footer -->
    <%= render 'shared/footer' %>
  </body>
</html>
