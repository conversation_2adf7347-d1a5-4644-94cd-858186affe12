<%# 
  Reusable status badge component
  
  Parameters:
  - status: Status value (active, inactive, pending, success, warning, error, etc.)
  - text: Custom text to display (optional, defaults to status.humanize)
  - size: Size variant (sm, md, lg)
  - variant: Style variant (solid, outline, soft)
  - icon: Optional icon name
%>

<% 
  size ||= 'md'
  variant ||= 'soft'
  text ||= status.to_s.humanize
  
  # Status-based color mapping
  status_colors = {
    'active' => 'green',
    'inactive' => 'gray',
    'pending' => 'yellow',
    'draft' => 'gray',
    'completed' => 'green',
    'paused' => 'yellow',
    'cancelled' => 'red',
    'success' => 'green',
    'warning' => 'yellow',
    'error' => 'red',
    'danger' => 'red',
    'info' => 'blue',
    'primary' => 'blue',
    'excellent' => 'green',
    'good' => 'blue',
    'fair' => 'yellow',
    'poor' => 'red',
    'high' => 'red',
    'medium' => 'yellow',
    'low' => 'green'
  }
  
  color = status_colors[status.to_s.downcase] || 'gray'
  
  # Size classes
  size_classes = {
    'sm' => {
      text: 'text-xs',
      padding: 'px-2 py-1',
      icon: 'w-3 h-3'
    },
    'md' => {
      text: 'text-sm',
      padding: 'px-2.5 py-1.5',
      icon: 'w-4 h-4'
    },
    'lg' => {
      text: 'text-base',
      padding: 'px-3 py-2',
      icon: 'w-5 h-5'
    }
  }
  
  # Variant and color classes
  variant_classes = {
    'solid' => {
      'green' => 'bg-green-600 text-white',
      'blue' => 'bg-blue-600 text-white',
      'yellow' => 'bg-yellow-600 text-white',
      'red' => 'bg-red-600 text-white',
      'gray' => 'bg-gray-600 text-white'
    },
    'outline' => {
      'green' => 'border border-green-600 text-green-600 bg-white',
      'blue' => 'border border-blue-600 text-blue-600 bg-white',
      'yellow' => 'border border-yellow-600 text-yellow-600 bg-white',
      'red' => 'border border-red-600 text-red-600 bg-white',
      'gray' => 'border border-gray-600 text-gray-600 bg-white'
    },
    'soft' => {
      'green' => 'bg-green-100 text-green-800 border border-green-200',
      'blue' => 'bg-blue-100 text-blue-800 border border-blue-200',
      'yellow' => 'bg-yellow-100 text-yellow-800 border border-yellow-200',
      'red' => 'bg-red-100 text-red-800 border border-red-200',
      'gray' => 'bg-gray-100 text-gray-800 border border-gray-200'
    }
  }
  
  current_size = size_classes[size]
  current_variant = variant_classes[variant][color]
%>

<span class="inline-flex items-center <%= current_size[:padding] %> rounded-full <%= current_size[:text] %> font-medium <%= current_variant %> whitespace-nowrap">
  <% if local_assigns[:icon].present? %>
    <%= render 'shared/icons/heroicon', name: icon, class: "#{current_size[:icon]} mr-1.5" %>
  <% end %>

  <%= text %>
</span>
