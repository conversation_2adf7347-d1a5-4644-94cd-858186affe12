<%# 
  Reusable metric card component for dashboard
  
  Parameters:
  - title: Card title/label
  - value: Main metric value
  - subtitle: Additional context text
  - trend: Trend indicator (positive, negative, neutral)
  - trend_value: Trend percentage/value
  - icon: SVG icon name (optional)
  - color_scheme: Color theme (default, success, warning, danger)
%>

<% 
  color_scheme ||= 'default'
  trend ||= 'neutral'
  
  # Color scheme classes
  color_classes = {
    'default' => {
      bg: 'bg-white',
      border: 'border-gray-200',
      title: 'text-gray-600',
      value: 'text-gray-900',
      subtitle: 'text-gray-500'
    },
    'success' => {
      bg: 'bg-green-50',
      border: 'border-green-200',
      title: 'text-green-700',
      value: 'text-green-900',
      subtitle: 'text-green-600'
    },
    'warning' => {
      bg: 'bg-amber-50',
      border: 'border-amber-200',
      title: 'text-amber-700',
      value: 'text-amber-900',
      subtitle: 'text-amber-600'
    },
    'danger' => {
      bg: 'bg-red-50',
      border: 'border-red-200',
      title: 'text-red-700',
      value: 'text-red-900',
      subtitle: 'text-red-600'
    },
    'vibrant_success' => {
      bg: 'bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50',
      border: 'border-emerald-300 shadow-emerald-100',
      title: 'text-emerald-700',
      value: 'text-emerald-900',
      subtitle: 'text-emerald-600'
    },
    'vibrant_purple' => {
      bg: 'bg-gradient-to-br from-purple-50 via-violet-50 to-indigo-50',
      border: 'border-purple-300 shadow-purple-100',
      title: 'text-purple-700',
      value: 'text-purple-900',
      subtitle: 'text-purple-600'
    },
    'vibrant_blue' => {
      bg: 'bg-gradient-to-br from-blue-50 via-sky-50 to-cyan-50',
      border: 'border-blue-300 shadow-blue-100',
      title: 'text-blue-700',
      value: 'text-blue-900',
      subtitle: 'text-blue-600'
    },
    'vibrant_pink' => {
      bg: 'bg-gradient-to-br from-pink-50 via-rose-50 to-red-50',
      border: 'border-pink-300 shadow-pink-100',
      title: 'text-pink-700',
      value: 'text-pink-900',
      subtitle: 'text-pink-600'
    },
    'vibrant_orange' => {
      bg: 'bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50',
      border: 'border-orange-300 shadow-orange-100',
      title: 'text-orange-700',
      value: 'text-orange-900',
      subtitle: 'text-orange-600'
    },
    'vibrant_cyan' => {
      bg: 'bg-gradient-to-br from-cyan-50 via-teal-50 to-emerald-50',
      border: 'border-cyan-300 shadow-cyan-100',
      title: 'text-cyan-700',
      value: 'text-cyan-900',
      subtitle: 'text-cyan-600'
    },
    'vibrant_emerald' => {
      bg: 'bg-gradient-to-br from-emerald-50 via-green-50 to-lime-50',
      border: 'border-emerald-300 shadow-emerald-100',
      title: 'text-emerald-700',
      value: 'text-emerald-900',
      subtitle: 'text-emerald-600'
    },
    'vibrant_lime' => {
      bg: 'bg-gradient-to-br from-lime-50 via-green-50 to-emerald-50',
      border: 'border-lime-300 shadow-lime-100',
      title: 'text-lime-700',
      value: 'text-lime-900',
      subtitle: 'text-lime-600'
    },
    'vibrant_gold' => {
      bg: 'bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50',
      border: 'border-yellow-300 shadow-yellow-100',
      title: 'text-yellow-700',
      value: 'text-yellow-900',
      subtitle: 'text-yellow-600'
    }
  }
  
  # Trend indicator classes
  trend_classes = {
    'positive' => 'text-green-600 bg-green-100',
    'negative' => 'text-red-600 bg-red-100',
    'neutral' => 'text-gray-600 bg-gray-100'
  }
  
  current_colors = color_classes[color_scheme]
%>

<div class="<%= current_colors[:bg] %> <%= current_colors[:border] %> border rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-105">
  <!-- Header with title and trend -->
  <div class="flex items-center justify-between mb-4">
    <div class="flex items-center space-x-3">
      <% if local_assigns[:icon].present? %>
        <div class="p-2 <%= current_colors[:bg] == 'bg-white' ? 'bg-gray-100' : 'bg-white' %> rounded-lg">
          <%= render 'shared/icons/heroicon', name: icon, class: "w-5 h-5 #{current_colors[:title]}" %>
        </div>
      <% end %>
      <h3 class="text-sm font-medium <%= current_colors[:title] %> uppercase tracking-wide">
        <%= title %>
      </h3>
    </div>

    <% if local_assigns[:trend_value].present? %>
      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= trend_classes[trend] %>">
        <% if trend == 'positive' %>
          <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
          </svg>
        <% elsif trend == 'negative' %>
          <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
          </svg>
        <% else %>
          <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
          </svg>
        <% end %>
        <%= trend_value %>
      </span>
    <% end %>
  </div>

  <!-- Main value -->
  <div class="mb-2">
    <p class="text-3xl font-bold <%= current_colors[:value] %> tracking-tight" data-dashboard-target="metric">
      <%= value %>
    </p>
  </div>

  <!-- Subtitle/context -->
  <% if local_assigns[:subtitle].present? %>
    <p class="text-sm <%= current_colors[:subtitle] %> font-medium">
      <%= subtitle %>
    </p>
  <% end %>
  
  <!-- Optional content block -->
  <% if content_for?(:metric_card_content) %>
    <div class="mt-4">
      <%= yield :metric_card_content %>
    </div>
  <% end %>
</div>
