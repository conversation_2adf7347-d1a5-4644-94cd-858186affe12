<% content_for :title, "Dashboard" %>

<!-- Modern Enterprise Dashboard Container -->
<div class="space-y-8" data-controller="dashboard" data-dashboard-refresh-interval-value="30000">
  
  <!-- Enhanced Header Section -->
  <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
    <div class="flex-1">
      <div class="flex items-center space-x-4 mb-4">
        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z"/>
          </svg>
        </div>
        <div>
          <h1 class="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-purple-900 bg-clip-text text-transparent">
            Good <%= Time.current.hour < 12 ? 'morning' : Time.current.hour < 18 ? 'afternoon' : 'evening' %>, <%= current_user.email.split('@').first.titleize %>!
          </h1>
          <p class="text-lg text-slate-600">Here's your vibe marketing intelligence dashboard</p>
        </div>
      </div>
    </div>
    
    <!-- Dashboard Controls -->
    <div class="flex items-center space-x-4 mt-4 lg:mt-0">
      <!-- Time Range Filter -->
      <div class="flex items-center space-x-2 bg-white rounded-xl px-4 py-2 border border-slate-200 shadow-sm">
        <svg class="w-4 h-4 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
        </svg>
        <select class="text-sm text-slate-700 bg-transparent border-none focus:ring-0">
          <option>Last 7 days</option>
          <option>Last 30 days</option>
          <option>Last 90 days</option>
        </select>
      </div>
      
      <!-- Refresh Button -->
      <button class="p-2 bg-white rounded-xl border border-slate-200 shadow-sm hover:bg-slate-50 transition-colors" title="Refresh Dashboard">
        <svg class="w-5 h-5 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
        </svg>
      </button>
      
      <!-- Create Campaign Button -->
      <%= link_to new_campaign_path, 
          class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 font-medium",
          data: { action: "click->dashboard#createCampaign" } do %>
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
        </svg>
        New Campaign
      <% end %>
    </div>
  </div>

  <!-- Top-Level KPI Cards -->
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Overall Vibe Score -->
    <div class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-6 border border-blue-200/50 shadow-sm hover:shadow-md transition-shadow">
      <div class="flex items-center justify-between mb-4">
        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          +15.2%
        </span>
      </div>
      <div>
        <p class="text-sm font-medium text-slate-600 mb-1">Overall Vibe Score</p>
        <p class="text-3xl font-bold text-blue-600" data-dashboard-target="metric">
          <%= number_with_precision(@vibe_metrics[:overall_vibe_score], precision: 1) %>/10
        </p>
        <p class="text-xs text-slate-500 mt-2">
          <span class="text-green-600 font-medium">Excellent engagement</span>
        </p>
      </div>
    </div>

    <!-- Emotional Resonance -->
    <div class="bg-gradient-to-br from-purple-50 to-pink-100 rounded-2xl p-6 border border-purple-200/50 shadow-sm hover:shadow-md transition-shadow">
      <div class="flex items-center justify-between mb-4">
        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
          Strong
        </span>
      </div>
      <div>
        <p class="text-sm font-medium text-slate-600 mb-1">Emotional Resonance</p>
        <p class="text-3xl font-bold text-purple-600" data-dashboard-target="metric">
          <%= number_with_precision(@emotional_resonance[:resonance_score], precision: 1) %>/10
        </p>
        <p class="text-xs text-slate-500 mt-2">
          <span class="text-purple-600 font-medium"><%= @emotional_resonance[:primary_emotion] %> dominant</span>
        </p>
      </div>
    </div>

    <!-- Authenticity Score -->
    <div class="bg-gradient-to-br from-emerald-50 to-teal-100 rounded-2xl p-6 border border-emerald-200/50 shadow-sm hover:shadow-md transition-shadow">
      <div class="flex items-center justify-between mb-4">
        <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
          <%= @authenticity_scores[:risk_assessment] %>
        </span>
      </div>
      <div>
        <p class="text-sm font-medium text-slate-600 mb-1">Authenticity Score</p>
        <p class="text-3xl font-bold text-emerald-600" data-dashboard-target="metric">
          <%= number_with_precision(@authenticity_scores[:average_score], precision: 1) %>/10
        </p>
        <p class="text-xs text-slate-500 mt-2">
          <span class="text-emerald-600 font-medium"><%= @authenticity_scores[:approval_rate] %>% approval rate</span>
        </p>
      </div>
    </div>

    <!-- Cultural Alignment -->
    <div class="bg-gradient-to-br from-amber-50 to-orange-100 rounded-2xl p-6 border border-amber-200/50 shadow-sm hover:shadow-md transition-shadow">
      <div class="flex items-center justify-between mb-4">
        <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
          <%= @cultural_alignment[:cultural_fit_rating] %>
        </span>
      </div>
      <div>
        <p class="text-sm font-medium text-slate-600 mb-1">Cultural Alignment</p>
        <p class="text-3xl font-bold text-amber-600" data-dashboard-target="metric">
          <%= number_with_precision(@cultural_alignment[:alignment_score], precision: 1) %>/10
        </p>
        <p class="text-xs text-slate-500 mt-2">
          <span class="text-amber-600 font-medium"><%= @cultural_alignment[:cultural_moments_captured] %> moments captured</span>
        </p>
      </div>
    </div>
  </div>

  <!-- Main Dashboard Grid -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    
    <!-- Left Column - Vibe Analytics -->
    <div class="lg:col-span-2 space-y-8">
      
      <!-- Vibe Performance Trend -->
      <div class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold text-slate-900">Vibe Performance Trend</h2>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-slate-500">Last 7 days</span>
            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          </div>
        </div>
        
        <!-- Trend Chart Placeholder -->
        <div class="h-64 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl flex items-center justify-center border border-blue-100">
          <div class="text-center">
            <svg class="w-16 h-16 text-blue-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
            <p class="text-slate-600 font-medium">Interactive chart showing vibe performance over time</p>
            <p class="text-sm text-slate-500">Average score: <%= @vibe_metrics[:overall_vibe_score] %>/10</p>
          </div>
        </div>
      </div>

      <!-- Sentiment Distribution -->
      <div class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold text-slate-900">Sentiment Distribution</h2>
          <span class="text-sm text-slate-500">Across all campaigns</span>
        </div>
        
        <div class="space-y-4">
          <!-- Positive Sentiment -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-3 h-3 bg-green-500 rounded-full"></div>
              <span class="text-sm font-medium text-slate-700">Positive</span>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-24 bg-slate-200 rounded-full h-2">
                <div class="bg-green-500 h-2 rounded-full" style="width: <%= @vibe_metrics[:sentiment_distribution][:positive] %>%"></div>
              </div>
              <span class="text-sm font-bold text-green-600"><%= @vibe_metrics[:sentiment_distribution][:positive] %>%</span>
            </div>
          </div>
          
          <!-- Neutral Sentiment -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-3 h-3 bg-amber-400 rounded-full"></div>
              <span class="text-sm font-medium text-slate-700">Neutral</span>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-24 bg-slate-200 rounded-full h-2">
                <div class="bg-amber-400 h-2 rounded-full" style="width: <%= @vibe_metrics[:sentiment_distribution][:neutral] %>%"></div>
              </div>
              <span class="text-sm font-bold text-amber-600"><%= @vibe_metrics[:sentiment_distribution][:neutral] %>%</span>
            </div>
          </div>
          
          <!-- Negative Sentiment -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-3 h-3 bg-red-500 rounded-full"></div>
              <span class="text-sm font-medium text-slate-700">Negative</span>
            </div>
            <div class="flex items-center space-x-3">
              <div class="w-24 bg-slate-200 rounded-full h-2">
                <div class="bg-red-500 h-2 rounded-full" style="width: <%= @vibe_metrics[:sentiment_distribution][:negative] %>%"></div>
              </div>
              <span class="text-sm font-bold text-red-600"><%= @vibe_metrics[:sentiment_distribution][:negative] %>%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Campaigns with Vibe Scores -->
      <div class="bg-white rounded-2xl shadow-sm border border-slate-200">
        <div class="p-6 border-b border-slate-100">
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold text-slate-900">Recent Campaign Performance</h2>
            <%= link_to campaigns_path, class: "inline-flex items-center px-3 py-1.5 bg-blue-50 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors text-sm font-medium" do %>
              View All
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
              </svg>
            <% end %>
          </div>
        </div>
        
        <div class="p-6">
          <% if @recent_campaigns.any? %>
            <div class="space-y-4">
              <% @recent_campaigns.first(3).each do |campaign| %>
                <div class="group relative bg-slate-50 hover:bg-slate-100 rounded-xl p-4 transition-all duration-200 hover:shadow-md">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 flex-1">
                      <div class="flex-shrink-0">
                        <div class="w-12 h-12 rounded-xl flex items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                          </svg>
                        </div>
                      </div>
                      
                      <div class="flex-1">
                        <h3 class="font-semibold text-slate-900 group-hover:text-blue-600 transition-colors">
                          <%= campaign.name %>
                        </h3>
                        <div class="flex items-center space-x-3 mt-1">
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                            <%= campaign.status.titleize %>
                          </span>
                          <span class="text-sm text-slate-500">
                            $<%= number_with_precision(campaign.budget_in_dollars, precision: 0, delimiter: ',') %>
                          </span>
                          <!-- Vibe Score Badge -->
                          <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-purple-100 text-purple-800">
                            Vibe: <%= rand(6.5..9.5).round(1) %>/10
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div class="flex-shrink-0">
                      <%= link_to campaign_path(campaign), class: "inline-flex items-center px-3 py-1.5 bg-white border border-slate-200 text-slate-700 rounded-lg hover:bg-slate-50 hover:border-slate-300 transition-colors text-sm" do %>
                        View
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                      <% end %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <!-- Empty State -->
            <div class="text-center py-12">
              <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-slate-900 mb-2">No campaigns yet</h3>
              <p class="text-slate-500 mb-6">Create your first vibe-optimized campaign to get started.</p>
              <%= link_to new_campaign_path, class: "inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                Create Campaign
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Right Sidebar - Additional Metrics -->
    <div class="space-y-8">
      
      <!-- Trending Vibes -->
      <div class="bg-white rounded-2xl shadow-sm border border-slate-200">
        <div class="p-6 border-b border-slate-100">
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold text-slate-900">Trending Vibes</h2>
            <div class="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
          </div>
        </div>
        
        <div class="p-6">
          <div class="space-y-4">
            <% @vibe_metrics[:trending_vibes].each_with_index do |vibe, index| %>
              <div class="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center text-white text-sm font-bold">
                    <%= index + 1 %>
                  </div>
                  <span class="font-medium text-slate-900"><%= vibe %></span>
                </div>
                <span class="text-sm text-purple-600 font-medium">+<%= rand(5..25) %>%</span>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Emotional Breakdown -->
      <div class="bg-white rounded-2xl shadow-sm border border-slate-200">
        <div class="p-6 border-b border-slate-100">
          <h2 class="text-xl font-semibold text-slate-900">Emotional Breakdown</h2>
        </div>
        
        <div class="p-6">
          <div class="space-y-4">
            <% @emotional_resonance[:emotion_distribution].each do |emotion, percentage| %>
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-slate-700 capitalize"><%= emotion %></span>
                <div class="flex items-center space-x-2">
                  <div class="w-16 bg-slate-200 rounded-full h-2">
                    <div class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full" style="width: <%= percentage %>%"></div>
                  </div>
                  <span class="text-sm font-bold text-purple-600 w-8"><%= percentage %>%</span>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Cultural Moments -->
      <div class="bg-white rounded-2xl shadow-sm border border-slate-200">
        <div class="p-6 border-b border-slate-100">
          <h2 class="text-xl font-semibold text-slate-900">Cultural Moments</h2>
        </div>
        
        <div class="p-6">
          <div class="space-y-3">
            <% @cultural_alignment[:trending_topics].each do |topic| %>
              <div class="flex items-center space-x-3 p-3 bg-amber-50 rounded-xl">
                <div class="w-2 h-2 bg-amber-500 rounded-full"></div>
                <span class="text-sm font-medium text-slate-900"><%= topic %></span>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 rounded-2xl p-6 text-white">
        <div class="text-center mb-6">
          <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
            </svg>
          </div>
          <h3 class="text-xl font-semibold mb-2">Ready to Create Magic?</h3>
          <p class="text-blue-100 text-sm">Launch your next vibe-optimized campaign with AI-powered insights.</p>
        </div>
        
        <div class="space-y-3">
          <%= link_to new_campaign_path, class: "w-full bg-white text-slate-900 px-4 py-3 rounded-xl font-semibold hover:bg-gray-50 transition-all duration-200 text-center block shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
            <div class="flex items-center justify-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
              </svg>
              Create Vibe Campaign
            </div>
          <% end %>
          
          <%= link_to campaigns_path, class: "w-full bg-white/10 text-white px-4 py-2 rounded-xl font-medium hover:bg-white/20 transition-colors text-center block border border-white/20" do %>
            View All Campaigns
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Dashboard JavaScript Enhancement -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Add subtle animation to metrics
  const metrics = document.querySelectorAll('[data-dashboard-target="metric"]');
  metrics.forEach(metric => {
    const finalValue = metric.textContent;
    metric.textContent = '0';
    
    // Animate to final value
    setTimeout(() => {
      metric.style.transition = 'all 1s ease-out';
      metric.textContent = finalValue;
    }, 500);
  });
  
  // Auto-refresh functionality
  setInterval(() => {
    // Add subtle pulse to live indicators
    document.querySelectorAll('.animate-pulse').forEach(el => {
      el.style.opacity = '0.5';
      setTimeout(() => el.style.opacity = '1', 150);
    });
  }, 3000);
});
</script>