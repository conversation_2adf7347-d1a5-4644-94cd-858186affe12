<% content_for :title, "Dashboard" %>

<!-- Modern Dashboard Container -->
<div class="bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 pb-12 relative overflow-hidden" data-controller="dashboard" data-dashboard-refresh-interval-value="30000">
  <!-- Animated Background Elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse"></div>
    <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-cyan-400/10 to-teal-400/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 4s;"></div>
  </div>

  <!-- Enhanced Header Section -->
  <div class="bg-gradient-to-r from-white/90 via-purple-50/90 to-blue-50/90 backdrop-blur-sm sticky top-0 z-10 mb-0 shadow-lg">
    <!-- Header Background Pattern -->
    <div class="absolute inset-0 bg-gradient-to-r from-purple-500/5 via-pink-500/5 to-blue-500/5"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="flex-1">
          <div class="flex items-center space-x-4 mb-2">
            <div class="p-3 bg-gradient-to-br from-purple-500 via-pink-500 to-blue-600 rounded-xl shadow-lg animate-pulse">
              <%= render 'shared/icons/heroicon', name: 'presentation-chart-line', variant: 'solid', class: 'w-8 h-8 text-white' %>
            </div>
            <div>
              <h1 class="text-3xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 bg-clip-text text-transparent">
                Good <%= Time.current.hour < 12 ? 'morning' : Time.current.hour < 18 ? 'afternoon' : 'evening' %>, <%= current_user.email.split('@').first.titleize %>
              </h1>
              <p class="text-gray-700 text-lg font-medium">AI Marketing Intelligence Dashboard</p>
            </div>
          </div>
        </div>

        <div class="flex items-center space-x-4 mt-6 lg:mt-0">
          <!-- Enhanced Time Range Selector -->
          <div class="flex items-center space-x-3 bg-gradient-to-r from-white to-purple-50 rounded-lg px-4 py-2 shadow-lg backdrop-blur-sm">
            <%= render 'shared/icons/heroicon', name: 'chart-bar', class: 'w-4 h-4 text-purple-500' %>
            <label class="text-sm text-purple-700 font-semibold">Period:</label>
            <%= select_tag :time_range,
                options_for_select([
                  ['Last 7 days', '7d'],
                  ['Last 30 days', '30d'],
                  ['Last 90 days', '90d']
                ], '7d'),
                class: "text-sm border-0 bg-transparent text-purple-800 font-semibold focus:ring-0 cursor-pointer" %>
          </div>

          <!-- Enhanced Action Buttons -->
          <div class="flex items-center space-x-2">
            <button class="p-2 text-purple-400 hover:text-white hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500 rounded-lg transition-all duration-200 shadow-lg border border-purple-200/50 backdrop-blur-sm" title="Refresh Dashboard">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
            </button>

            <button class="p-2 text-blue-400 hover:text-white hover:bg-gradient-to-r hover:from-blue-500 hover:to-cyan-500 rounded-lg transition-all duration-200 shadow-lg border border-blue-200/50 backdrop-blur-sm" title="Dashboard Settings">
              <%= render 'shared/icons/heroicon', name: 'cog-6-tooth', class: 'w-5 h-5' %>
            </button>
          </div>

          <!-- Enhanced New Campaign Button -->
          <%= link_to new_campaign_path, class: "inline-flex items-center space-x-2 bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:from-purple-700 hover:via-pink-700 hover:to-blue-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 hover:scale-105 animate-pulse" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <span>New Campaign</span>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Modern KPI Cards Section -->
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Overall Vibe Score -->
      <%= render 'dashboard/metric_card',
          title: 'Vibe Score',
          value: number_with_precision(@vibe_metrics[:overall_vibe_score], precision: 1),
          subtitle: 'Excellent engagement',
          trend: 'positive',
          trend_value: '+15.2%',
          icon: 'heart',
          color_scheme: 'vibrant_success' %>

      <!-- Emotional Resonance -->
      <%= render 'dashboard/metric_card',
          title: 'Emotional Resonance',
          value: number_with_precision(@emotional_resonance[:resonance_score], precision: 1),
          subtitle: "#{@emotional_resonance[:primary_emotion]} dominant",
          trend: 'positive',
          trend_value: 'Strong',
          icon: 'users',
          color_scheme: 'vibrant_purple' %>

      <!-- Authenticity Score -->
      <%= render 'dashboard/metric_card',
          title: 'Authenticity',
          value: number_with_precision(@authenticity_scores[:average_score], precision: 1),
          subtitle: "#{@authenticity_scores[:approval_rate]}% approval rate",
          trend: @authenticity_scores[:risk_assessment] == 'Low' ? 'positive' : 'neutral',
          trend_value: @authenticity_scores[:risk_assessment],
          icon: 'shield-check',
          color_scheme: @authenticity_scores[:risk_assessment] == 'Low' ? 'vibrant_blue' : 'vibrant_orange' %>

      <!-- Cultural Alignment -->
      <%= render 'dashboard/metric_card',
          title: 'Cultural Alignment',
          value: number_with_precision(@cultural_alignment[:alignment_score], precision: 1),
          subtitle: "#{@cultural_alignment[:cultural_moments_captured]} moments captured",
          trend: 'positive',
          trend_value: @cultural_alignment[:cultural_fit_rating],
          icon: 'globe-alt',
          color_scheme: 'vibrant_pink' %>
    </div>

    <!-- Additional Performance Metrics Row -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
      <!-- Total Campaigns -->
      <%= render 'dashboard/metric_card',
          title: 'Total Campaigns',
          value: number_with_delimiter(@campaign_stats[:total]),
          subtitle: "#{@campaign_stats[:active]} active",
          trend: 'positive',
          trend_value: "+#{@campaign_stats[:active]}",
          icon: 'chart-bar',
          color_scheme: 'vibrant_cyan' %>

      <!-- Total Budget -->
      <%= render 'dashboard/metric_card',
          title: 'Total Budget',
          value: "$#{number_with_delimiter(@budget_stats[:total_budget].round)}",
          subtitle: "$#{number_with_delimiter(@budget_stats[:spent_budget].round)} spent",
          trend: 'neutral',
          trend_value: "#{((@budget_stats[:spent_budget] / @budget_stats[:total_budget]) * 100).round}%",
          icon: 'currency-dollar',
          color_scheme: 'vibrant_emerald' %>

      <!-- Success Rate -->
      <%= render 'dashboard/metric_card',
          title: 'Success Rate',
          value: "#{@performance_metrics[:success_rate]}%",
          subtitle: 'Campaign performance',
          trend: 'positive',
          trend_value: @performance_metrics[:improvement_trend] || '+8.2%',
          icon: 'arrow-trending-up',
          color_scheme: 'vibrant_lime' %>

      <!-- ROI -->
      <%= render 'dashboard/metric_card',
          title: 'Average ROI',
          value: "#{@performance_metrics[:avg_roi]}%",
          subtitle: 'Return on investment',
          trend: 'positive',
          trend_value: '+12.5%',
          icon: 'banknotes',
          color_scheme: 'vibrant_gold' %>
    </div>
  </div>

  <!-- Main Content Grid -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-16">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

      <!-- Left Column - Analytics -->
      <div class="lg:col-span-2 space-y-8">

        <!-- Performance Trend Chart -->
        <%= render 'dashboard/chart_container',
            title: 'Performance Trend',
            subtitle: 'Campaign performance over time',
            chart_type: 'line',
            height: 'h-80',
            data: @vibe_metrics[:vibe_performance_trend] do %>
          <% content_for :chart_footer do %>
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-600">Average Score: <%= @vibe_metrics[:overall_vibe_score] %>/10</span>
              <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                  <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span class="text-gray-600">Vibe Score</span>
                </div>
                <div class="flex items-center space-x-2">
                  <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span class="text-gray-600">Engagement</span>
                </div>
              </div>
            </div>
          <% end %>
        <% end %>

        <!-- Sentiment Distribution -->
        <div class="bg-white rounded-xl border border-gray-200 shadow-sm">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Sentiment Distribution</h3>
                <p class="text-sm text-gray-600 mt-1">Campaign sentiment analysis</p>
              </div>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                All Campaigns
              </span>
            </div>
          </div>

          <div class="p-6">
            <div class="space-y-6">
              <!-- Positive Sentiment -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                  <span class="text-gray-700 font-medium">Positive</span>
                </div>
                <div class="flex items-center space-x-4">
                  <%= render 'dashboard/progress_bar',
                      value: @vibe_metrics[:sentiment_distribution][:positive],
                      color: 'success',
                      size: 'sm',
                      show_percentage: false %>
                  <span class="text-gray-900 font-semibold w-12 text-right"><%= @vibe_metrics[:sentiment_distribution][:positive] %>%</span>
                </div>
              </div>

              <!-- Neutral Sentiment -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-4 h-4 bg-gray-400 rounded-full"></div>
                  <span class="text-gray-700 font-medium">Neutral</span>
                </div>
                <div class="flex items-center space-x-4">
                  <%= render 'dashboard/progress_bar',
                      value: @vibe_metrics[:sentiment_distribution][:neutral],
                      color: 'gray',
                      size: 'sm',
                      show_percentage: false %>
                  <span class="text-gray-900 font-semibold w-12 text-right"><%= @vibe_metrics[:sentiment_distribution][:neutral] %>%</span>
                </div>
              </div>

              <!-- Negative Sentiment -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-4 h-4 bg-red-400 rounded-full"></div>
                  <span class="text-gray-700 font-medium">Negative</span>
                </div>
                <div class="flex items-center space-x-4">
                  <%= render 'dashboard/progress_bar',
                      value: @vibe_metrics[:sentiment_distribution][:negative],
                      color: 'danger',
                      size: 'sm',
                      show_percentage: false %>
                  <span class="text-gray-900 font-semibold w-12 text-right"><%= @vibe_metrics[:sentiment_distribution][:negative] %>%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Campaigns -->
        <div class="bg-white rounded-xl border border-gray-200 shadow-sm">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Recent Campaigns</h3>
                <p class="text-sm text-gray-600 mt-1">Latest campaign activity</p>
              </div>
              <%= link_to campaigns_path, class: "inline-flex items-center space-x-1 text-blue-600 hover:text-blue-700 transition-colors text-sm font-medium" do %>
                <span>View All</span>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              <% end %>
            </div>
          </div>

          <div class="p-6">
            <% if @recent_campaigns.any? %>
              <div class="space-y-4">
                <% @recent_campaigns.first(4).each_with_index do |campaign, index| %>
                  <div class="group p-4 rounded-lg border border-gray-100 hover:border-gray-200 hover:shadow-sm transition-all duration-200">
                    <div class="flex items-center justify-between">
                      <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                          <h4 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                            <%= campaign.name %>
                          </h4>
                          <%= render 'dashboard/status_badge', status: campaign.status, size: 'sm' %>
                        </div>
                        <div class="flex items-center space-x-6 text-sm text-gray-500">
                          <div class="flex items-center space-x-1">
                            <%= render 'shared/icons/heroicon', name: 'currency-dollar', class: 'w-4 h-4' %>
                            <span>$<%= number_with_delimiter(campaign.budget_in_dollars.round) %></span>
                          </div>
                          <div class="flex items-center space-x-1">
                            <%= render 'shared/icons/heroicon', name: 'chart-bar', class: 'w-4 h-4' %>
                            <span>Score: <%= rand(6.5..9.5).round(1) %>/10</span>
                          </div>
                          <div class="flex items-center space-x-1">
                            <%= render 'shared/icons/heroicon', name: 'calendar', class: 'w-4 h-4' %>
                            <span><%= campaign.updated_at.strftime('%b %d') %></span>
                          </div>
                        </div>
                      </div>

                      <div class="flex items-center space-x-2">
                        <%= link_to campaign_path(campaign), class: "p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200" do %>
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                          </svg>
                        <% end %>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            <% else %>
              <!-- Enhanced Empty State -->
              <div class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <%= render 'shared/icons/heroicon', name: 'chart-bar', class: 'w-8 h-8 text-gray-400' %>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">No campaigns yet</h3>
                <p class="text-gray-600 mb-6">Create your first campaign to get started with AI-powered marketing</p>
                <%= link_to new_campaign_path, class: "inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl" do %>
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  <span>Create Campaign</span>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Right Sidebar -->
      <div class="space-y-8">

        <!-- Trending Vibes -->
        <div class="bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50 rounded-xl border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300">
          <div class="px-6 py-4 border-b border-purple-200/50 bg-gradient-to-r from-purple-100/50 to-pink-100/50">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-lg font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Trending Vibes</h3>
                <p class="text-sm text-purple-700 mt-1 font-medium">Popular emotional tones</p>
              </div>
              <div class="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full animate-pulse shadow-lg"></div>
            </div>
          </div>

          <div class="p-6">
            <div class="space-y-4">
              <% @vibe_metrics[:trending_vibes].each_with_index do |vibe, index| %>
                <%
                  gradient_colors = [
                    'from-purple-500 to-pink-500',
                    'from-blue-500 to-cyan-500',
                    'from-green-500 to-emerald-500',
                    'from-orange-500 to-red-500',
                    'from-indigo-500 to-purple-500'
                  ]
                  current_gradient = gradient_colors[index % gradient_colors.length]
                %>
                <div class="flex items-center justify-between p-3 rounded-lg bg-gradient-to-r from-white to-purple-50/50 hover:from-purple-50 hover:to-pink-50 transition-all duration-300 border border-purple-100 hover:border-purple-200 shadow-sm hover:shadow-md">
                  <div class="flex items-center space-x-3">
                    <div class="flex items-center justify-center w-8 h-8 bg-gradient-to-br <%= current_gradient %> text-white text-sm font-bold rounded-full shadow-lg animate-pulse">
                      <%= index + 1 %>
                    </div>
                    <span class="font-bold text-purple-900"><%= vibe %></span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="text-sm font-bold text-emerald-600 bg-emerald-100 px-2 py-1 rounded-full">+<%= rand(5..25) %>%</span>
                    <svg class="w-5 h-5 text-emerald-500 animate-bounce" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Emotional Breakdown -->
        <div class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-xl border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300">
          <div class="px-6 py-4 border-b border-blue-200/50 bg-gradient-to-r from-blue-100/50 to-indigo-100/50">
            <h3 class="text-lg font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Emotional Breakdown</h3>
            <p class="text-sm text-blue-700 mt-1 font-medium">Campaign emotional distribution</p>
          </div>

          <div class="p-6">
            <div class="space-y-4">
              <%
                emotion_colors = {
                  'joy' => 'warning',
                  'trust' => 'primary',
                  'anticipation' => 'success',
                  'surprise' => 'danger',
                  'other' => 'gray'
                }
              %>
              <% @emotional_resonance[:emotion_distribution].each_with_index do |(emotion, percentage), index| %>
                <div class="space-y-2 p-3 rounded-lg bg-gradient-to-r from-white/80 to-blue-50/80 border border-blue-100 hover:border-blue-200 transition-all duration-200">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                      <div class="w-3 h-3 rounded-full bg-gradient-to-r <%=
                        case emotion.to_s
                        when 'joy' then 'from-yellow-400 to-orange-500'
                        when 'trust' then 'from-blue-400 to-indigo-500'
                        when 'anticipation' then 'from-green-400 to-emerald-500'
                        when 'surprise' then 'from-pink-400 to-red-500'
                        else 'from-gray-400 to-gray-500'
                        end
                      %> animate-pulse"></div>
                      <span class="font-bold text-blue-900 capitalize"><%= emotion %></span>
                    </div>
                    <span class="text-sm font-bold text-blue-900 bg-blue-100 px-2 py-1 rounded-full"><%= percentage %>%</span>
                  </div>
                  <%= render 'dashboard/progress_bar',
                      value: percentage,
                      color: emotion_colors[emotion.to_s] || 'primary',
                      size: 'sm',
                      show_percentage: false %>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Cultural Moments -->
        <div class="bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 rounded-xl border border-emerald-200 shadow-lg hover:shadow-xl transition-all duration-300">
          <div class="px-6 py-4 border-b border-emerald-200/50 bg-gradient-to-r from-emerald-100/50 to-teal-100/50">
            <h3 class="text-lg font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">Cultural Moments</h3>
            <p class="text-sm text-emerald-700 mt-1 font-medium">Trending cultural topics</p>
          </div>

          <div class="p-6">
            <div class="space-y-3">
              <%
                moment_gradients = [
                  'from-purple-500 to-pink-500',
                  'from-blue-500 to-cyan-500',
                  'from-green-500 to-emerald-500',
                  'from-orange-500 to-red-500',
                  'from-indigo-500 to-purple-500'
                ]
              %>
              <% @cultural_alignment[:trending_topics].each_with_index do |topic, index| %>
                <% current_gradient = moment_gradients[index % moment_gradients.length] %>
                <div class="flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-white/80 to-emerald-50/80 hover:from-emerald-50 hover:to-teal-50 transition-all duration-300 border border-emerald-100 hover:border-emerald-200 shadow-sm hover:shadow-md">
                  <div class="w-3 h-3 bg-gradient-to-r <%= current_gradient %> rounded-full animate-pulse shadow-lg"></div>
                  <span class="font-bold text-emerald-900 flex-1"><%= topic %></span>
                  <span class="text-xs text-emerald-700 bg-gradient-to-r from-emerald-100 to-teal-100 px-3 py-1 rounded-full font-bold border border-emerald-200 animate-pulse">🔥 Trending</span>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Enhanced Quick Actions -->
        <div class="bg-gradient-to-br from-purple-900 via-pink-900 to-indigo-900 rounded-xl shadow-2xl text-white overflow-hidden relative">
          <!-- Animated Background -->
          <div class="absolute inset-0 bg-gradient-to-br from-purple-600/30 via-pink-600/30 to-blue-600/30 animate-pulse"></div>
          <div class="absolute inset-0">
            <div class="absolute top-0 left-0 w-20 h-20 bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-full blur-xl animate-bounce"></div>
            <div class="absolute bottom-0 right-0 w-16 h-16 bg-gradient-to-br from-green-400/20 to-emerald-500/20 rounded-full blur-xl animate-bounce" style="animation-delay: 1s;"></div>
          </div>

          <div class="relative p-8">
            <div class="text-center mb-8">
              <div class="w-20 h-20 bg-gradient-to-br from-purple-500/20 via-pink-500/20 to-blue-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/20 animate-pulse">
                <%= render 'shared/icons/heroicon', name: 'chart-bar', variant: 'solid', class: 'w-10 h-10 text-white' %>
              </div>
              <h3 class="text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-300 via-pink-300 to-cyan-300 bg-clip-text text-transparent">Ready to Create Magic? ✨</h3>
              <p class="text-purple-100 text-sm font-medium">Launch your next AI-powered campaign with intelligent insights</p>
            </div>

            <div class="space-y-4">
              <%= link_to new_campaign_path, class: "w-full bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 text-white px-6 py-4 rounded-lg font-bold hover:from-yellow-500 hover:via-pink-600 hover:to-purple-700 transition-all duration-300 text-center block shadow-2xl hover:shadow-3xl transform hover:-translate-y-1 hover:scale-105 animate-pulse" do %>
                <div class="flex items-center justify-center space-x-2">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  <span>🚀 Create Campaign</span>
                </div>
              <% end %>

              <%= link_to campaigns_path, class: "w-full text-white px-6 py-3 rounded-lg font-bold bg-gradient-to-r from-blue-500/20 to-purple-500/20 hover:from-blue-500/30 hover:to-purple-500/30 transition-all duration-300 text-center block border border-white/30 hover:border-white/50 backdrop-blur-sm shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
                <div class="flex items-center justify-center space-x-2">
                  <%= render 'shared/icons/heroicon', name: 'chart-bar', class: 'w-5 h-5' %>
                  <span>📊 View All Campaigns</span>
                </div>
              <% end %>

              <%= link_to "#", class: "w-full text-white px-6 py-3 rounded-lg font-bold bg-gradient-to-r from-emerald-500/20 to-cyan-500/20 hover:from-emerald-500/30 hover:to-cyan-500/30 transition-all duration-300 text-center block border border-white/30 hover:border-white/50 backdrop-blur-sm shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
                <div class="flex items-center justify-center space-x-2">
                  <%= render 'shared/icons/heroicon', name: 'cog-6-tooth', class: 'w-5 h-5' %>
                  <span>⚙️ Dashboard Settings</span>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced Dashboard JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Enhanced metric animations with staggered loading
  const metrics = document.querySelectorAll('[data-dashboard-target="metric"]');
  metrics.forEach((metric, index) => {
    metric.style.opacity = '0';
    metric.style.transform = 'translateY(20px)';

    setTimeout(() => {
      metric.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
      metric.style.opacity = '1';
      metric.style.transform = 'translateY(0)';
    }, 100 + (index * 150)); // Staggered animation
  });

  // Animate progress bars
  const progressBars = document.querySelectorAll('[role="progressbar"]');
  progressBars.forEach((bar, index) => {
    const width = bar.style.width;
    bar.style.width = '0%';

    setTimeout(() => {
      bar.style.transition = 'width 1.2s ease-out';
      bar.style.width = width;
    }, 500 + (index * 100));
  });

  // Add hover effects to cards
  const cards = document.querySelectorAll('.bg-white.rounded-xl');
  cards.forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-2px)';
      this.style.boxShadow = '0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
    });

    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
      this.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)';
    });
  });

  // Auto-refresh functionality
  const refreshInterval = 30000; // 30 seconds
  let refreshTimer;

  function refreshDashboard() {
    // Add subtle loading indicator
    const refreshButton = document.querySelector('[title="Refresh Dashboard"]');
    if (refreshButton) {
      refreshButton.classList.add('animate-spin');

      // Simulate refresh (in real implementation, this would fetch new data)
      setTimeout(() => {
        refreshButton.classList.remove('animate-spin');

        // Show success indicator
        const originalHTML = refreshButton.innerHTML;
        refreshButton.innerHTML = '<svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';

        setTimeout(() => {
          refreshButton.innerHTML = originalHTML;
        }, 1000);
      }, 1000);
    }
  }

  // Set up auto-refresh
  function startAutoRefresh() {
    refreshTimer = setInterval(refreshDashboard, refreshInterval);
  }

  function stopAutoRefresh() {
    if (refreshTimer) {
      clearInterval(refreshTimer);
    }
  }

  // Start auto-refresh
  startAutoRefresh();

  // Pause auto-refresh when page is not visible
  document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
      stopAutoRefresh();
    } else {
      startAutoRefresh();
    }
  });

  // Manual refresh button
  const refreshButton = document.querySelector('[title="Refresh Dashboard"]');
  if (refreshButton) {
    refreshButton.addEventListener('click', function(e) {
      e.preventDefault();
      refreshDashboard();
    });
  }
});
</script>