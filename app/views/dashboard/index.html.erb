<% content_for :title, "Dashboard" %>

<!-- Clean Dashboard Container -->
<div class="space-y-12" data-controller="dashboard" data-dashboard-refresh-interval-value="30000">
  
  <!-- Minimalist Header -->
  <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
    <div class="flex-1">
      <h1 class="text-5xl font-light text-slate-900 mb-2">
        Good <%= Time.current.hour < 12 ? 'morning' : Time.current.hour < 18 ? 'afternoon' : 'evening' %>, <%= current_user.email.split('@').first.titleize %>
      </h1>
      <p class="text-slate-500 text-lg font-light">Marketing Intelligence Dashboard</p>
    </div>
    
    <!-- Clean Controls -->
    <div class="flex items-center space-x-3 mt-6 lg:mt-0">
      <select class="text-sm text-slate-600 bg-transparent focus:outline-none">
        <option>Last 7 days</option>
        <option>Last 30 days</option>
        <option>Last 90 days</option>
      </select>
      
      <button class="p-2 text-slate-600 hover:text-slate-900 transition-colors" title="Refresh">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
        </svg>
      </button>
      
      <%= link_to new_campaign_path, 
          class: "inline-flex items-center px-6 py-2 bg-slate-900 text-white hover:bg-slate-800 transition-colors font-medium",
          data: { action: "click->dashboard#createCampaign" } do %>
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
        </svg>
        New Campaign
      <% end %>
    </div>
  </div>

  <!-- Clean KPI Cards -->
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
    <!-- Overall Vibe Score -->
    <div class="bg-white p-8">
      <div class="flex items-center justify-between mb-6">
        <p class="text-sm text-slate-500 uppercase tracking-wide">Vibe Score</p>
        <span class="text-sm text-green-600 font-medium">+15.2%</span>
      </div>
      <div>
        <p class="text-4xl font-light text-slate-900 mb-2" data-dashboard-target="metric">
          <%= number_with_precision(@vibe_metrics[:overall_vibe_score], precision: 1) %>
        </p>
        <p class="text-sm text-slate-500">Excellent engagement</p>
      </div>
    </div>

    <!-- Emotional Resonance -->
    <div class="bg-white p-8">
      <div class="flex items-center justify-between mb-6">
        <p class="text-sm text-slate-500 uppercase tracking-wide">Emotional Resonance</p>
        <span class="text-sm text-slate-600 font-medium">Strong</span>
      </div>
      <div>
        <p class="text-4xl font-light text-slate-900 mb-2" data-dashboard-target="metric">
          <%= number_with_precision(@emotional_resonance[:resonance_score], precision: 1) %>
        </p>
        <p class="text-sm text-slate-500"><%= @emotional_resonance[:primary_emotion] %> dominant</p>
      </div>
    </div>

    <!-- Authenticity Score -->
    <div class="bg-white p-8">
      <div class="flex items-center justify-between mb-6">
        <p class="text-sm text-slate-500 uppercase tracking-wide">Authenticity</p>
        <span class="text-sm text-slate-600 font-medium"><%= @authenticity_scores[:risk_assessment] %></span>
      </div>
      <div>
        <p class="text-4xl font-light text-slate-900 mb-2" data-dashboard-target="metric">
          <%= number_with_precision(@authenticity_scores[:average_score], precision: 1) %>
        </p>
        <p class="text-sm text-slate-500"><%= @authenticity_scores[:approval_rate] %>% approval rate</p>
      </div>
    </div>

    <!-- Cultural Alignment -->
    <div class="bg-white p-8">
      <div class="flex items-center justify-between mb-6">
        <p class="text-sm text-slate-500 uppercase tracking-wide">Cultural Alignment</p>
        <span class="text-sm text-slate-600 font-medium"><%= @cultural_alignment[:cultural_fit_rating] %></span>
      </div>
      <div>
        <p class="text-4xl font-light text-slate-900 mb-2" data-dashboard-target="metric">
          <%= number_with_precision(@cultural_alignment[:alignment_score], precision: 1) %>
        </p>
        <p class="text-sm text-slate-500"><%= @cultural_alignment[:cultural_moments_captured] %> moments captured</p>
      </div>
    </div>
  </div>

  <!-- Main Content Grid -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
    
    <!-- Left Column - Analytics -->
    <div class="lg:col-span-2 space-y-12">
      
      <!-- Performance Trend -->
      <div class="bg-white p-8">
        <div class="flex items-center justify-between mb-8">
          <h2 class="text-2xl font-light text-slate-900">Performance Trend</h2>
          <div class="flex items-center space-x-3">
            <span class="text-sm text-slate-500">Last 7 days</span>
            <div class="w-2 h-2 bg-green-500"></div>
          </div>
        </div>
        
        <!-- Clean Chart Area -->
        <div class="h-64 bg-slate-50 flex items-center justify-center">
          <div class="text-center">
            <p class="text-slate-600 mb-2">Performance visualization</p>
            <p class="text-sm text-slate-400">Average score: <%= @vibe_metrics[:overall_vibe_score] %>/10</p>
          </div>
        </div>
      </div>

      <!-- Sentiment Distribution -->
      <div class="bg-white p-8">
        <div class="flex items-center justify-between mb-8">
          <h2 class="text-2xl font-light text-slate-900">Sentiment Distribution</h2>
          <span class="text-sm text-slate-400">All campaigns</span>
        </div>
        
        <div class="space-y-6">
          <!-- Positive Sentiment -->
          <div class="flex items-center justify-between">
            <span class="text-slate-700 font-medium">Positive</span>
            <div class="flex items-center space-x-4">
              <div class="w-32 bg-slate-100 h-1">
                <div class="bg-slate-900 h-1" style="width: <%= @vibe_metrics[:sentiment_distribution][:positive] %>%"></div>
              </div>
              <span class="text-slate-900 font-medium w-12 text-right"><%= @vibe_metrics[:sentiment_distribution][:positive] %>%</span>
            </div>
          </div>
          
          <!-- Neutral Sentiment -->
          <div class="flex items-center justify-between">
            <span class="text-slate-700 font-medium">Neutral</span>
            <div class="flex items-center space-x-4">
              <div class="w-32 bg-slate-100 h-1">
                <div class="bg-slate-600 h-1" style="width: <%= @vibe_metrics[:sentiment_distribution][:neutral] %>%"></div>
              </div>
              <span class="text-slate-900 font-medium w-12 text-right"><%= @vibe_metrics[:sentiment_distribution][:neutral] %>%</span>
            </div>
          </div>
          
          <!-- Negative Sentiment -->
          <div class="flex items-center justify-between">
            <span class="text-slate-700 font-medium">Negative</span>
            <div class="flex items-center space-x-4">
              <div class="w-32 bg-slate-100 h-1">
                <div class="bg-slate-400 h-1" style="width: <%= @vibe_metrics[:sentiment_distribution][:negative] %>%"></div>
              </div>
              <span class="text-slate-900 font-medium w-12 text-right"><%= @vibe_metrics[:sentiment_distribution][:negative] %>%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Campaigns -->
      <div class="bg-white p-8">
        <div class="flex items-center justify-between mb-8">
          <h2 class="text-2xl font-light text-slate-900">Recent Campaigns</h2>
          <%= link_to campaigns_path, class: "text-slate-600 hover:text-slate-900 transition-colors text-sm font-medium" do %>
            View All →
          <% end %>
        </div>
        
        <div>
          <% if @recent_campaigns.any? %>
            <div class="space-y-6">
              <% @recent_campaigns.first(3).each do |campaign| %>
                <div class="group py-4 border-b border-slate-100 last:border-b-0">
                  <div class="flex items-center justify-between">
                    <div class="flex-1">
                      <h3 class="font-medium text-slate-900 mb-2">
                        <%= campaign.name %>
                      </h3>
                      <div class="flex items-center space-x-6 text-sm text-slate-500">
                        <span class="font-medium text-slate-700"><%= campaign.status.titleize %></span>
                        <span>$<%= number_with_precision(campaign.budget_in_dollars, precision: 0, delimiter: ',') %></span>
                        <span>Score: <%= rand(6.5..9.5).round(1) %>/10</span>
                      </div>
                    </div>
                    
                    <div>
                      <%= link_to campaign_path(campaign), class: "text-slate-600 hover:text-slate-900 transition-colors text-sm font-medium" do %>
                        View →
                      <% end %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <!-- Empty State -->
            <div class="text-center py-16">
              <h3 class="text-lg font-light text-slate-900 mb-4">No campaigns yet</h3>
              <p class="text-slate-500 mb-8">Create your first campaign to get started</p>
              <%= link_to new_campaign_path, class: "inline-flex items-center px-6 py-2 bg-slate-900 text-white hover:bg-slate-800 transition-colors font-medium" do %>
                Create Campaign
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Right Sidebar -->
    <div class="space-y-12">
      
      <!-- Trending Vibes -->
      <div class="bg-white p-8">
        <div class="flex items-center justify-between mb-8">
          <h2 class="text-xl font-light text-slate-900">Trending Vibes</h2>
          <div class="w-2 h-2 bg-slate-400"></div>
        </div>
        
        <div class="space-y-4">
          <% @vibe_metrics[:trending_vibes].each_with_index do |vibe, index| %>
            <div class="flex items-center justify-between py-2">
              <div class="flex items-center space-x-4">
                <span class="text-sm text-slate-400 font-mono w-4"><%= index + 1 %></span>
                <span class="font-medium text-slate-900"><%= vibe %></span>
              </div>
              <span class="text-sm text-slate-600">+<%= rand(5..25) %>%</span>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Emotional Breakdown -->
      <div class="bg-white p-8">
        <h2 class="text-xl font-light text-slate-900 mb-8">Emotional Breakdown</h2>
        
        <div class="space-y-4">
          <% @emotional_resonance[:emotion_distribution].each do |emotion, percentage| %>
            <div class="flex items-center justify-between">
              <span class="font-medium text-slate-700 capitalize"><%= emotion %></span>
              <div class="flex items-center space-x-4">
                <div class="w-16 bg-slate-100 h-1">
                  <div class="bg-slate-900 h-1" style="width: <%= percentage %>%"></div>
                </div>
                <span class="text-sm font-medium text-slate-900 w-8"><%= percentage %>%</span>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Cultural Moments -->
      <div class="bg-white p-8">
        <h2 class="text-xl font-light text-slate-900 mb-8">Cultural Moments</h2>
        
        <div class="space-y-3">
          <% @cultural_alignment[:trending_topics].each do |topic| %>
            <div class="flex items-center space-x-3 py-2">
              <div class="w-1 h-1 bg-slate-400"></div>
              <span class="font-medium text-slate-900"><%= topic %></span>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-slate-900 p-8 text-white">
        <div class="text-center mb-8">
          <h3 class="text-xl font-light mb-4">Ready to Create?</h3>
          <p class="text-slate-300 text-sm font-light">Launch your next campaign with AI insights</p>
        </div>
        
        <div class="space-y-4">
          <%= link_to new_campaign_path, class: "w-full bg-white text-slate-900 px-6 py-3 font-medium hover:bg-slate-100 transition-colors text-center block" do %>
            Create Campaign
          <% end %>
          
          <%= link_to campaigns_path, class: "w-full text-white px-6 py-2 font-medium hover:text-slate-300 transition-colors text-center block border border-slate-700 hover:border-slate-600" do %>
            View All Campaigns
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Clean Dashboard JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Subtle metric animation
  const metrics = document.querySelectorAll('[data-dashboard-target="metric"]');
  metrics.forEach(metric => {
    metric.style.opacity = '0';
    setTimeout(() => {
      metric.style.transition = 'opacity 0.8s ease';
      metric.style.opacity = '1';
    }, 200);
  });
});
</script>