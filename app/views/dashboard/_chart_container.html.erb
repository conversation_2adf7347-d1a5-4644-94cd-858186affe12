<%# 
  Reusable chart container component
  
  Parameters:
  - title: Chart title
  - subtitle: Optional subtitle/description
  - height: Chart height class (default: h-64)
  - data: Chart data (for future chart integration)
  - chart_type: Type of chart (line, bar, pie, etc.)
  - loading: Whether to show loading state
  - empty_state_message: Message to show when no data
%>

<% 
  height ||= 'h-64'
  loading ||= false
  chart_type ||= 'line'
  empty_state_message ||= 'No data available'
%>

<div class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
  <!-- Chart Header -->
  <div class="px-6 py-4 bg-gray-50 rounded-t-lg">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold text-gray-900"><%= title %></h3>
        <% if local_assigns[:subtitle].present? %>
          <p class="text-sm text-gray-600 mt-1"><%= subtitle %></p>
        <% end %>
      </div>

      <!-- Chart controls/legend -->
      <div class="flex items-center space-x-3">
        <% if local_assigns[:chart_type].present? %>
          <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <%= chart_type.humanize %>
          </span>
        <% end %>

        <!-- Optional chart controls -->
        <div class="flex items-center space-x-2">
          <button type="button" class="p-2 text-gray-500 hover:text-gray-700 hover:bg-white rounded-lg transition-all duration-200 shadow-sm" title="Refresh Chart">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
          </button>

          <button type="button" class="p-2 text-gray-500 hover:text-gray-700 hover:bg-white rounded-lg transition-all duration-200 shadow-sm" title="Chart Options">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Chart Content Area -->
  <div class="p-6">
    <div class="<%= height %> relative">
      <% if loading %>
        <!-- Loading State -->
        <div class="absolute inset-0 flex items-center justify-center bg-gray-50 rounded-lg">
          <div class="text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-3"></div>
            <p class="text-sm text-gray-600">Loading chart data...</p>
          </div>
        </div>
      <% elsif local_assigns[:data].present? %>
        <!-- Chart Data Available -->
        <div class="w-full h-full bg-blue-50 rounded-lg flex items-center justify-center shadow-inner">
          <div class="text-center">
            <svg class="w-12 h-12 text-blue-500 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <p class="text-blue-700 font-medium">Chart Visualization</p>
            <p class="text-blue-600 text-sm mt-1">Data visualization will appear here</p>
          </div>
        </div>

        <!-- Chart data for JavaScript integration -->
        <script type="application/json" data-chart-data>
          <%= raw data.to_json if local_assigns[:data].present? %>
        </script>
      <% else %>
        <!-- Empty State -->
        <div class="absolute inset-0 flex items-center justify-center bg-gray-50 rounded-lg">
          <div class="text-center">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <p class="text-gray-600 font-medium">No Data Available</p>
            <p class="text-gray-500 text-sm mt-1"><%= empty_state_message %></p>
          </div>
        </div>
      <% end %>
    </div>
    
    <!-- Optional chart footer/legend -->
    <% if content_for?(:chart_footer) %>
      <div class="mt-4 pt-4 bg-gray-50 rounded-lg">
        <%= yield :chart_footer %>
      </div>
    <% end %>
  </div>
</div>
