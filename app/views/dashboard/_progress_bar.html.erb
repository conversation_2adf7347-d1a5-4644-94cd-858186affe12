<%# 
  Reusable progress bar component
  
  Parameters:
  - label: Progress bar label
  - value: Current value (0-100 for percentage, or actual value)
  - max_value: Maximum value (optional, defaults to 100 for percentage)
  - color: Color theme (primary, success, warning, danger)
  - size: Size variant (sm, md, lg)
  - show_percentage: Whether to show percentage text
  - animated: Whether to animate the progress bar
%>

<% 
  color ||= 'primary'
  size ||= 'md'
  max_value ||= 100
  show_percentage ||= true
  animated ||= false
  
  # Calculate percentage
  percentage = max_value > 0 ? [(value.to_f / max_value * 100).round(1), 100].min : 0
  
  # Color scheme classes
  color_classes = {
    'primary' => {
      bg: 'bg-blue-500',
      bg_light: 'bg-blue-100',
      text: 'text-blue-700'
    },
    'success' => {
      bg: 'bg-green-500',
      bg_light: 'bg-green-100',
      text: 'text-green-700'
    },
    'warning' => {
      bg: 'bg-amber-500',
      bg_light: 'bg-amber-100',
      text: 'text-amber-700'
    },
    'danger' => {
      bg: 'bg-red-500',
      bg_light: 'bg-red-100',
      text: 'text-red-700'
    },
    'gray' => {
      bg: 'bg-gray-500',
      bg_light: 'bg-gray-100',
      text: 'text-gray-700'
    }
  }
  
  # Size classes
  size_classes = {
    'sm' => 'h-2',
    'md' => 'h-3',
    'lg' => 'h-4'
  }
  
  current_colors = color_classes[color]
  height_class = size_classes[size]
%>

<div class="w-full">
  <!-- Label and percentage -->
  <% if local_assigns[:label].present? || show_percentage %>
    <div class="flex items-center justify-between mb-2">
      <% if local_assigns[:label].present? %>
        <span class="text-sm font-medium text-gray-700"><%= label %></span>
      <% end %>

      <% if show_percentage %>
        <span class="text-sm font-medium <%= current_colors[:text] %>">
          <% if max_value == 100 %>
            <%= percentage %>%
          <% else %>
            <%= number_with_delimiter(value) %> / <%= number_with_delimiter(max_value) %>
          <% end %>
        </span>
      <% end %>
    </div>
  <% end %>

  <!-- Progress bar container -->
  <div class="w-full <%= current_colors[:bg_light] %> rounded-full <%= height_class %> overflow-hidden">
    <!-- Progress bar fill -->
    <div
      class="<%= current_colors[:bg] %> <%= height_class %> rounded-full transition-all duration-500 ease-out <%= 'animate-pulse' if animated %>"
      style="width: <%= percentage %>%"
      role="progressbar"
      aria-valuenow="<%= value %>"
      aria-valuemin="0"
      aria-valuemax="<%= max_value %>"
      <% if local_assigns[:label].present? %>
        aria-label="<%= label %>"
      <% end %>
    >
      <!-- Optional gradient overlay for enhanced visual appeal -->
      <div class="w-full h-full bg-gradient-to-r from-transparent to-white opacity-20 rounded-full"></div>
    </div>
  </div>

  <!-- Optional additional content -->
  <% if content_for?(:progress_bar_content) %>
    <div class="mt-2">
      <%= yield :progress_bar_content %>
    </div>
  <% end %>
</div>
