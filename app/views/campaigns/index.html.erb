<% content_for :title, "Campaigns" %>

<!-- Clean Campaigns Container -->
<div class="space-y-12">
  
  <!-- Minimalist Header -->
  <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
    <div class="flex-1">
      <h1 class="text-5xl font-light text-slate-900 mb-2">Campaigns</h1>
      <p class="text-slate-500 text-lg font-light">Orchestrate powerful marketing campaigns</p>
      
      <!-- Clean Stats Bar -->
      <div class="flex items-center space-x-8 mt-6 text-sm">
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-slate-900"></div>
          <span class="text-slate-700 font-medium"><%= @campaign_stats[:active] %> Active</span>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-slate-600"></div>
          <span class="text-slate-700 font-medium">$<%= number_with_delimiter(@campaigns.sum(:budget_cents) / 100) %> Budget</span>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 bg-slate-400"></div>
          <span class="text-slate-700 font-medium"><%= @campaign_stats[:total] %> Total</span>
        </div>
      </div>
    </div>
    
    <!-- Clean Action Buttons -->
    <div class="flex items-center space-x-4 mt-6 lg:mt-0">
      <%= link_to new_campaign_path, class: "inline-flex items-center px-6 py-2 bg-slate-900 text-white hover:bg-slate-800 transition-colors font-medium" do %>
        Create Campaign
      <% end %>
      
      <button class="inline-flex items-center px-6 py-2 text-slate-600 hover:text-slate-900 border border-slate-300 hover:border-slate-600 transition-colors font-medium">
        Analytics
      </button>
    </div>
  </div>

  <!-- Clean Stats Grid -->
  <div class="grid grid-cols-2 md:grid-cols-5 gap-8">
    <!-- Total Campaigns -->
    <div class="bg-white p-6">
      <p class="text-sm text-slate-500 uppercase tracking-wide mb-4">Total</p>
      <p class="text-3xl font-light text-slate-900"><%= @campaign_stats[:total] %></p>
      <p class="text-sm text-slate-500 mt-2">All campaigns</p>
    </div>

    <!-- Active Campaigns -->
    <div class="bg-white p-6">
      <p class="text-sm text-slate-500 uppercase tracking-wide mb-4">Active</p>
      <p class="text-3xl font-light text-slate-900"><%= @campaign_stats[:active] %></p>
      <p class="text-sm text-slate-500 mt-2">Running now</p>
    </div>

    <!-- Draft Campaigns -->
    <div class="bg-white p-6">
      <p class="text-sm text-slate-500 uppercase tracking-wide mb-4">Draft</p>
      <p class="text-3xl font-light text-slate-900"><%= @campaign_stats[:draft] %></p>
      <p class="text-sm text-slate-500 mt-2">In preparation</p>
    </div>

    <!-- Completed Campaigns -->
    <div class="bg-white p-6">
      <p class="text-sm text-slate-500 uppercase tracking-wide mb-4">Completed</p>
      <p class="text-3xl font-light text-slate-900"><%= @campaign_stats[:completed] %></p>
      <p class="text-sm text-slate-500 mt-2">Finished</p>
    </div>

    <!-- Paused Campaigns -->
    <div class="bg-white p-6">
      <p class="text-sm text-slate-500 uppercase tracking-wide mb-4">Paused</p>
      <p class="text-3xl font-light text-slate-900"><%= @campaign_stats[:paused] %></p>
      <p class="text-sm text-slate-500 mt-2">On hold</p>
    </div>
  </div>

  <!-- Campaign Management Section -->
  <div class="bg-white p-8">
    
    <!-- Filter and Sort Controls -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 space-y-4 sm:space-y-0">
      <div class="flex items-center space-x-6">
        <select class="text-sm text-slate-600 bg-transparent focus:outline-none">
          <option>All Campaigns</option>
          <option>Active</option>
          <option>Draft</option>
          <option>Completed</option>
          <option>Paused</option>
        </select>
        
        <select class="text-sm text-slate-600 bg-transparent focus:outline-none">
          <option>All Types</option>
          <option>Email</option>
          <option>Social</option>
          <option>SEO</option>
          <option>Multi-channel</option>
        </select>
      </div>
      
      <div class="flex items-center space-x-4">
        <select class="text-sm text-slate-600 bg-transparent focus:outline-none">
          <option>Sort by: Recent</option>
          <option>Sort by: Name</option>
          <option>Sort by: Budget</option>
          <option>Sort by: Status</option>
        </select>
        
        <button class="p-2 text-slate-600 hover:text-slate-900 transition-colors" title="Toggle View">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Campaign List -->
    <% if @campaigns.any? %>
      <div class="space-y-1">
        <% @campaigns.each do |campaign| %>
          <div class="group py-4 border-b border-slate-100 last:border-b-0 hover:bg-slate-50 transition-colors">
            <div class="flex items-center justify-between">
              
              <!-- Campaign Info -->
              <div class="flex-1">
                <div class="flex items-center space-x-4">
                  <h3 class="font-medium text-slate-900 group-hover:text-slate-700">
                    <%= campaign.name %>
                  </h3>
                  <span class="text-sm text-slate-500 font-medium px-2 py-1 bg-slate-100">
                    <%= campaign.status.titleize %>
                  </span>
                  <span class="text-sm text-slate-500 font-medium px-2 py-1 bg-slate-100">
                    <%= campaign.campaign_type.titleize.gsub('_', ' ') %>
                  </span>
                </div>
                
                <div class="flex items-center space-x-6 mt-2 text-sm text-slate-500">
                  <span>Budget: $<%= number_with_precision(campaign.budget_in_dollars, precision: 0, delimiter: ',') %></span>
                  <% if campaign.start_date %>
                    <span>Start: <%= campaign.start_date.strftime('%b %d, %Y') %></span>
                  <% end %>
                  <% if campaign.end_date %>
                    <span>End: <%= campaign.end_date.strftime('%b %d, %Y') %></span>
                  <% end %>
                  <span>Performance: <%= rand(6.5..9.5).round(1) %>/10</span>
                </div>
              </div>
              
              <!-- Actions -->
              <div class="flex items-center space-x-3">
                <%= link_to campaign_path(campaign), class: "text-slate-600 hover:text-slate-900 transition-colors text-sm font-medium" do %>
                  View →
                <% end %>
                
                <%= link_to edit_campaign_path(campaign), class: "text-slate-600 hover:text-slate-900 transition-colors text-sm font-medium" do %>
                  Edit
                <% end %>
                
                <% if campaign.draft? %>
                  <%= link_to activate_campaign_path(campaign), method: :patch, 
                      class: "text-slate-600 hover:text-slate-900 transition-colors text-sm font-medium" do %>
                    Activate
                  <% end %>
                <% elsif campaign.active? %>
                  <%= link_to pause_campaign_path(campaign), method: :patch,
                      class: "text-slate-600 hover:text-slate-900 transition-colors text-sm font-medium" do %>
                    Pause
                  <% end %>
                <% elsif campaign.paused? %>
                  <%= link_to activate_campaign_path(campaign), method: :patch,
                      class: "text-slate-600 hover:text-slate-900 transition-colors text-sm font-medium" do %>
                    Resume
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <!-- Empty State -->
      <div class="text-center py-16">
        <h3 class="text-xl font-light text-slate-900 mb-4">No campaigns yet</h3>
        <p class="text-slate-500 mb-8 font-light">Create your first campaign to start marketing</p>
        <%= link_to new_campaign_path, class: "inline-flex items-center px-6 py-2 bg-slate-900 text-white hover:bg-slate-800 transition-colors font-medium" do %>
          Create Campaign
        <% end %>
      </div>
    <% end %>
  </div>
</div>

<!-- Clean JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Subtle hover effects
  const campaignRows = document.querySelectorAll('.group');
  campaignRows.forEach(row => {
    row.addEventListener('mouseenter', function() {
      this.style.transition = 'background-color 0.2s ease';
    });
  });
});
</script>