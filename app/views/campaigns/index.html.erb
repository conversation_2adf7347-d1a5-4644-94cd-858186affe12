<% content_for :title, "Campaigns" %>

<!-- Clean Campaigns Container -->
<div class="bg-gray-50 min-h-screen relative overflow-hidden">


  <!-- Main Content Container -->
  <div class="relative z-10">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

      <!-- Page Header Section -->
      <div class="mb-8">
        <div class="bg-white/95 backdrop-blur-sm rounded-xl p-8 shadow-sm">
          <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-4 mb-4">
                <div class="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg shadow-sm">
                  <%= render 'shared/icons/heroicon', name: 'chart-bar', variant: 'solid', class: 'w-8 h-8 text-white' %>
                </div>
                <div>
                  <h1 class="text-3xl font-bold text-gray-900">
                    Marketing Campaigns
                  </h1>
                  <p class="text-gray-600 text-lg">Orchestrate powerful AI-driven marketing campaigns</p>
                </div>
              </div>

              <!-- Stats Bar -->
              <div class="flex flex-wrap items-center gap-4 mt-6">
                <div class="flex items-center space-x-3 bg-green-50 px-4 py-2 rounded-full shadow-sm">
                  <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span class="text-green-700 font-medium"><%= @campaign_stats[:active] %> Active</span>
                </div>
                <div class="flex items-center space-x-3 bg-blue-50 px-4 py-2 rounded-full shadow-sm">
                  <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span class="text-blue-700 font-medium">$<%= number_with_delimiter(@campaigns.sum(:budget_cents) / 100) %> Budget</span>
                </div>
                <div class="flex items-center space-x-3 bg-gray-50 px-4 py-2 rounded-full shadow-sm">
                  <div class="w-2 h-2 bg-gray-500 rounded-full"></div>
                  <span class="text-gray-700 font-medium"><%= @campaign_stats[:total] %> Total</span>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row items-center space-y-3 sm:space-y-0 sm:space-x-4 mt-6 lg:mt-0">
              <%= link_to new_campaign_path, class: "inline-flex items-center space-x-2 bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-all duration-200 shadow-sm hover:shadow-md" do %>
                <%= render 'shared/icons/heroicon', name: 'plus', class: 'w-5 h-5' %>
                <span>Create Campaign</span>
              <% end %>

              <button class="inline-flex items-center space-x-2 bg-white text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-all duration-200 shadow-sm hover:shadow-md">
                <%= render 'shared/icons/heroicon', name: 'chart-pie', class: 'w-5 h-5' %>
                <span>Analytics</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Stats Grid Section -->
      <div class="mb-8">

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6">
          <!-- Total Campaigns -->
          <%= render 'dashboard/metric_card',
              title: 'Total Campaigns',
              value: @campaign_stats[:total],
              subtitle: 'All campaigns',
              trend: 'positive',
              trend_value: '+100%',
              icon: 'chart-bar',
              color_scheme: 'vibrant_purple' %>

          <!-- Active Campaigns -->
          <%= render 'dashboard/metric_card',
              title: 'Active',
              value: @campaign_stats[:active],
              subtitle: 'Running now',
              trend: 'positive',
              trend_value: 'Live',
              icon: 'arrow-trending-up',
              color_scheme: 'vibrant_success' %>

          <!-- Draft Campaigns -->
          <%= render 'dashboard/metric_card',
              title: 'Draft',
              value: @campaign_stats[:draft],
              subtitle: 'In preparation',
              trend: 'neutral',
              trend_value: 'Ready',
              icon: 'cog-6-tooth',
              color_scheme: 'vibrant_blue' %>

          <!-- Completed Campaigns -->
          <%= render 'dashboard/metric_card',
              title: 'Completed',
              value: @campaign_stats[:completed],
              subtitle: 'Finished',
              trend: 'positive',
              trend_value: 'Done',
              icon: 'shield-check',
              color_scheme: 'vibrant_emerald' %>

          <!-- Paused Campaigns -->
          <%= render 'dashboard/metric_card',
              title: 'Paused',
              value: @campaign_stats[:paused],
              subtitle: 'On hold',
              trend: 'neutral',
              trend_value: 'Paused',
              icon: 'clock',
              color_scheme: 'vibrant_orange' %>
        </div>
      </div>

      <!-- Campaign Management Section -->
      <div class="mb-8">

    <!-- Enhanced Campaign Management Section -->
    <div class="bg-gradient-to-br from-white via-purple-50/30 to-blue-50/30 backdrop-blur-sm border border-purple-200/50 rounded-2xl p-8 shadow-xl">

      <!-- Enhanced Filter and Sort Controls -->
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8 space-y-6 lg:space-y-0">
        <!-- Filter Section -->
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center space-x-3 bg-gradient-to-r from-purple-100 to-pink-100 rounded-xl px-4 py-3 border border-purple-200 shadow-sm">
            <%= render 'shared/icons/heroicon', name: 'funnel', class: 'w-4 h-4 text-purple-600' %>
            <label class="text-sm text-purple-700 font-semibold">Status:</label>
            <select class="text-sm text-purple-800 bg-transparent focus:outline-none font-medium cursor-pointer">
              <option>All Campaigns</option>
              <option>🟢 Active</option>
              <option>📝 Draft</option>
              <option>✅ Completed</option>
              <option>⏸️ Paused</option>
            </select>
          </div>

          <div class="flex items-center space-x-3 bg-gradient-to-r from-blue-100 to-cyan-100 rounded-xl px-4 py-3 border border-blue-200 shadow-sm">
            <%= render 'shared/icons/heroicon', name: 'tag', class: 'w-4 h-4 text-blue-600' %>
            <label class="text-sm text-blue-700 font-semibold">Type:</label>
            <select class="text-sm text-blue-800 bg-transparent focus:outline-none font-medium cursor-pointer">
              <option>All Types</option>
              <option>📧 Email</option>
              <option>📱 Social</option>
              <option>🔍 SEO</option>
              <option>🌐 Multi-channel</option>
            </select>
          </div>

          <div class="flex items-center space-x-3 bg-gradient-to-r from-emerald-100 to-teal-100 rounded-xl px-4 py-3 border border-emerald-200 shadow-sm">
            <%= render 'shared/icons/heroicon', name: 'currency-dollar', class: 'w-4 h-4 text-emerald-600' %>
            <label class="text-sm text-emerald-700 font-semibold">Budget:</label>
            <select class="text-sm text-emerald-800 bg-transparent focus:outline-none font-medium cursor-pointer">
              <option>All Budgets</option>
              <option>💰 $0 - $1K</option>
              <option>💎 $1K - $10K</option>
              <option>🏆 $10K+</option>
            </select>
          </div>
        </div>

        <!-- Sort and View Controls -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-3 bg-gradient-to-r from-orange-100 to-yellow-100 rounded-xl px-4 py-3 border border-orange-200 shadow-sm">
            <%= render 'shared/icons/heroicon', name: 'bars-arrow-down', class: 'w-4 h-4 text-orange-600' %>
            <select class="text-sm text-orange-800 bg-transparent focus:outline-none font-medium cursor-pointer">
              <option>📅 Recent</option>
              <option>🔤 Name</option>
              <option>💰 Budget</option>
              <option>📊 Performance</option>
            </select>
          </div>

          <button class="p-3 bg-gradient-to-r from-pink-100 to-rose-100 text-pink-600 hover:from-pink-200 hover:to-rose-200 rounded-xl transition-all duration-200 border border-pink-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5" title="Toggle View">
            <%= render 'shared/icons/heroicon', name: 'squares-2x2', class: 'w-5 h-5' %>
          </button>

          <button class="p-3 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-600 hover:from-indigo-200 hover:to-purple-200 rounded-xl transition-all duration-200 border border-indigo-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5" title="Export">
            <%= render 'shared/icons/heroicon', name: 'arrow-down-tray', class: 'w-5 h-5' %>
          </button>
        </div>
      </div>

    <!-- Campaign List -->
    <% if @campaigns.any? %>
      <div class="space-y-1">
        <% @campaigns.each do |campaign| %>
          <div class="group py-4 border-b border-slate-100 last:border-b-0 hover:bg-slate-50 transition-colors">
            <div class="flex items-center justify-between">
              
              <!-- Campaign Info -->
              <div class="flex-1">
                <div class="flex items-center space-x-4">
                  <h3 class="font-medium text-slate-900 group-hover:text-slate-700">
                    <%= campaign.name %>
                  </h3>
                  <span class="text-sm text-slate-500 font-medium px-2 py-1 bg-slate-100">
                    <%= campaign.status.titleize %>
                  </span>
                  <span class="text-sm text-slate-500 font-medium px-2 py-1 bg-slate-100">
                    <%= campaign.campaign_type.titleize.gsub('_', ' ') %>
                  </span>
                </div>
                
                <div class="flex items-center space-x-6 mt-2 text-sm text-slate-500">
                  <span>Budget: $<%= number_with_precision(campaign.budget_in_dollars, precision: 0, delimiter: ',') %></span>
                  <% if campaign.start_date %>
                    <span>Start: <%= campaign.start_date.strftime('%b %d, %Y') %></span>
                  <% end %>
                  <% if campaign.end_date %>
                    <span>End: <%= campaign.end_date.strftime('%b %d, %Y') %></span>
                  <% end %>
                  <span>Performance: <%= rand(6.5..9.5).round(1) %>/10</span>
                </div>
              </div>
              
              <!-- Actions -->
              <div class="flex items-center space-x-3">
                <%= link_to campaign_path(campaign), class: "text-slate-600 hover:text-slate-900 transition-colors text-sm font-medium" do %>
                  View →
                <% end %>
                
                <%= link_to edit_campaign_path(campaign), class: "text-slate-600 hover:text-slate-900 transition-colors text-sm font-medium" do %>
                  Edit
                <% end %>
                
                <% if campaign.draft? %>
                  <%= link_to activate_campaign_path(campaign), method: :patch, 
                      class: "text-slate-600 hover:text-slate-900 transition-colors text-sm font-medium" do %>
                    Activate
                  <% end %>
                <% elsif campaign.active? %>
                  <%= link_to pause_campaign_path(campaign), method: :patch,
                      class: "text-slate-600 hover:text-slate-900 transition-colors text-sm font-medium" do %>
                    Pause
                  <% end %>
                <% elsif campaign.paused? %>
                  <%= link_to activate_campaign_path(campaign), method: :patch,
                      class: "text-slate-600 hover:text-slate-900 transition-colors text-sm font-medium" do %>
                    Resume
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <!-- Empty State -->
      <div class="text-center py-16">
        <h3 class="text-xl font-light text-slate-900 mb-4">No campaigns yet</h3>
        <p class="text-slate-500 mb-8 font-light">Create your first campaign to start marketing</p>
        <%= link_to new_campaign_path, class: "inline-flex items-center px-6 py-2 bg-slate-900 text-white hover:bg-slate-800 transition-colors font-medium" do %>
          Create Campaign
        <% end %>
      </div>
    <% end %>
  </div>
</div>

<!-- Clean JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Subtle hover effects
  const campaignRows = document.querySelectorAll('.group');
  campaignRows.forEach(row => {
    row.addEventListener('mouseenter', function() {
      this.style.transition = 'background-color 0.2s ease';
    });
  });
});
</script>