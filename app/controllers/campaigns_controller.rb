# frozen_string_literal: true

class CampaignsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_current_tenant
  before_action :set_campaign, only: [ :show, :edit, :update, :destroy, :activate, :pause, :complete ]

  def index
    # Use optimized query service for better performance
    query_service = CampaignQueryService.new(current_tenant)
    cache_service = CampaignCacheService.new(current_tenant)

    # Get filtered campaigns with optimized queries
    filters = {
      status: params[:status],
      type: params[:type],
      search: params[:search],
      sort: params[:sort],
      page: params[:page]
    }.compact

    @campaigns = query_service.filtered_campaigns(filters)

    # Pagination (if pagy gem is available)
    if respond_to?(:pagy) && !filters[:page]
      @pagy, @campaigns = pagy(@campaigns, items: 12)
    end

    # Get cached campaign statistics
    @campaign_stats = cache_service.cached_campaign_stats
  end

  def show
    # Use optimized query and caching services
    query_service = CampaignQueryService.new(current_tenant)
    cache_service = CampaignCacheService.new(current_tenant)

    begin
      # Get campaign with eager loaded associations
      @campaign = query_service.campaign_with_associations(params[:id])

      # Get cached performance summary
      @performance_summary = cache_service.cached_performance_summary(@campaign)

      # Get recent metrics for the chart
      @campaign_metrics = @campaign.campaign_metrics
                                  .where(metric_date: 30.days.ago..Date.current)
                                  .order(:metric_date)
    rescue ActiveRecord::StatementInvalid => e
      # Handle case when campaign_metrics table doesn't exist yet
      Rails.logger.warn "Campaign metrics table not found: #{e.message}"
      @campaign_metrics = []
      @performance_summary = {}
    end
  end

  def new
    @campaign = current_tenant.campaigns.build
    @campaign.created_by = current_user
  end

  def create
    # Validate input using contract
    contract_result = CampaignContract.new.call(campaign_contract_params.to_h)

    if contract_result.success?
      @campaign = current_tenant.campaigns.build(contract_result.to_h)
      @campaign.created_by = current_user

      if @campaign.save
        create_associated_campaign_type

        # Invalidate tenant-wide caches after creating new campaign
        cache_service = CampaignCacheService.new(current_tenant)
        cache_service.invalidate_tenant_aggregate_caches

        redirect_to @campaign, notice: "Campaign was successfully created."
      else
        # Handle ActiveRecord validation errors
        flash.now[:alert] = "Campaign could not be created: #{@campaign.errors.full_messages.join(', ')}"
        render :new, status: :unprocessable_entity
      end
    else
      # Handle contract validation errors
      @campaign = current_tenant.campaigns.build(campaign_contract_params.to_h)
      @campaign.created_by = current_user
      flash.now[:alert] = "Campaign validation failed: #{format_contract_errors(contract_result.errors)}"
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    # Edit campaign form
  end

  def update
    # Validate input using contract
    contract_result = CampaignContract.new.call(campaign_contract_params.to_h)

    if contract_result.success?
      if @campaign.update(contract_result.to_h)
        # Invalidate caches for this campaign after update
        cache_service = CampaignCacheService.new(current_tenant)
        cache_service.invalidate_campaign_caches(@campaign)

        redirect_to @campaign, notice: "Campaign was successfully updated."
      else
        # Handle ActiveRecord validation errors
        flash.now[:alert] = "Campaign could not be updated: #{@campaign.errors.full_messages.join(', ')}"
        render :edit, status: :unprocessable_entity
      end
    else
      # Handle contract validation errors
      flash.now[:alert] = "Campaign validation failed: #{format_contract_errors(contract_result.errors)}"
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    # Invalidate caches before destroying the campaign
    cache_service = CampaignCacheService.new(current_tenant)
    cache_service.invalidate_campaign_caches(@campaign)

    @campaign.destroy
    redirect_to campaigns_url, notice: "Campaign was successfully deleted."
  end

  def activate
    if @campaign.can_be_activated?
      @campaign.update(status: "active")
      redirect_to @campaign, notice: "Campaign activated successfully."
    else
      redirect_to @campaign, alert: "Campaign cannot be activated in its current state."
    end
  end

  def pause
    if @campaign.active?
      @campaign.update(status: "paused")
      redirect_to @campaign, notice: "Campaign paused successfully."
    else
      redirect_to @campaign, alert: "Only active campaigns can be paused."
    end
  end

  def complete
    if @campaign.active? || @campaign.paused?
      @campaign.update(status: "completed")
      redirect_to @campaign, notice: "Campaign completed successfully."
    else
      redirect_to @campaign, alert: "Campaign cannot be completed in its current state."
    end
  end

  private

  def set_current_tenant
    ActsAsTenant.current_tenant = current_user.tenant
  end

  def current_tenant
    current_user.tenant
  end

  def set_campaign
    @campaign = current_tenant.campaigns.find(params[:id])
  end

  def campaign_params
    permitted_params = params.require(:campaign).permit(
      :name, :description, :campaign_type, :target_audience,
      :start_date, :end_date, :budget_in_dollars, :status,
      settings: {}
    )

    # Convert budget_in_dollars to budget_cents if present
    if permitted_params[:budget_in_dollars].present?
      permitted_params[:budget_cents] = (permitted_params[:budget_in_dollars].to_f * 100).round
      permitted_params.delete(:budget_in_dollars)
    end

    permitted_params
  end

  def campaign_contract_params
    permitted_params = params.require(:campaign).permit(
      :name, :description, :campaign_type, :target_audience,
      :start_date, :end_date, :budget_in_dollars
    )

    # Convert budget_in_dollars to budget_cents for contract validation
    if permitted_params[:budget_in_dollars].present?
      permitted_params[:budget_cents] = (permitted_params[:budget_in_dollars].to_f * 100).round
      permitted_params.delete(:budget_in_dollars)
    end

    permitted_params
  end

  def format_contract_errors(errors)
    errors.to_h.map do |field, messages|
      "#{field.to_s.humanize}: #{Array(messages).join(', ')}"
    end.join("; ")
  end

  def calculate_performance_summary
    return {} if @campaign_metrics.nil? || @campaign_metrics.empty?

    {
      total_impressions: @campaign_metrics.sum(:impressions),
      total_clicks: @campaign_metrics.sum(:clicks),
      total_conversions: @campaign_metrics.sum(:conversions),
      total_revenue: @campaign_metrics.sum(:revenue_cents) / 100.0,
      total_cost: @campaign_metrics.sum(:cost_cents) / 100.0,
      average_ctr: calculate_average_ctr,
      average_conversion_rate: calculate_average_conversion_rate
    }
  end

  def calculate_average_ctr
    return 0.0 if @campaign_metrics.nil? || @campaign_metrics.empty?

    total_impressions = @campaign_metrics.sum(:impressions)
    total_clicks = @campaign_metrics.sum(:clicks)
    return 0.0 if total_impressions.zero?

    (total_clicks.to_f / total_impressions * 100).round(2)
  end

  def calculate_average_conversion_rate
    return 0.0 if @campaign_metrics.nil? || @campaign_metrics.empty?

    total_clicks = @campaign_metrics.sum(:clicks)
    total_conversions = @campaign_metrics.sum(:conversions)
    return 0.0 if total_clicks.zero?

    (total_conversions.to_f / total_clicks * 100).round(2)
  end

  def create_associated_campaign_type
    case @campaign.campaign_type
    when "email"
      # Create placeholder email campaign - will be configured later
      @campaign.create_email_campaign!(
        subject_line: "#{@campaign.name} - Email Campaign",
        content: "Email content for #{@campaign.name}",
        from_name: current_user.full_name,
        from_email: current_user.email
      )
    when "social"
      # Create placeholder social campaign - will be configured later
      @campaign.create_social_campaign!(
        platforms: [ "twitter" ],
        content_variants: { "twitter" => "Social content for #{@campaign.name}" }
      )
    when "seo"
      # Create placeholder SEO campaign - will be configured later
      @campaign.create_seo_campaign!(
        target_keywords: @campaign.target_audience,
        meta_title: @campaign.name,
        meta_description: @campaign.description || "SEO campaign for #{@campaign.name}",
        content_strategy: {
          "pillars" => [],
          "target_pages" => [],
          "content_calendar" => {}
        }
      )
    end
  rescue => e
    Rails.logger.error "Failed to create associated campaign type: #{e.message}"
    # Don't fail the main campaign creation if associated type fails
  end
end
