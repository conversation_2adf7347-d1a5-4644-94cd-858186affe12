source "https://rubygems.org"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", "~> 8.0.2"
# The modern asset pipeline for Rails [https://github.com/rails/propshaft]
gem "propshaft"
# Use postgresql as the database for Active Record
gem "pg", "~> 1.1"
# Use the Puma web server [https://github.com/puma/puma]
gem "puma", ">= 5.0"
# Use JavaScript with ESM import maps [https://github.com/rails/importmap-rails]
gem "importmap-rails"
# Hotwire's SPA-like page accelerator [https://turbo.hotwired.dev]
gem "turbo-rails"
# Hotwire's modest JavaScript framework [https://stimulus.hotwired.dev]
gem "stimulus-rails"
# Use Tailwind CSS [https://github.com/rails/tailwindcss-rails]
gem "tailwindcss-rails"
# Build JSON APIs with ease [https://github.com/rails/jbuilder]
gem "jbuilder"

# Authentication & Authorization
gem "devise"
gem "pundit"

# Multi-tenancy
gem "acts_as_tenant"

# AI & External API Integration
gem "ruby_llm", "~> 1.3.0" # Multi-provider AI integration
gem "gibbon" # Mailchimp API client
gem "faraday" # HTTP client for external APIs
gem "faraday-retry" # Retry middleware for API calls
gem "faraday-multipart" # File upload support for AI attachments

# Enterprise Features
gem "redis" # Caching and session storage
gem "flipper" # Feature flags
gem "flipper-redis" # Redis backend for feature flags
gem "flipper-ui" # Web UI for feature flags
gem "sentry-ruby" # Error tracking and monitoring
gem "sentry-rails" # Rails integration for Sentry

# Input Validation
gem "dry-validation" # Advanced input validation with contracts

# Background Processing
gem "solid_queue"

# Data Processing & Validation
gem "dry-monads"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ mingw mswin x64_mingw jruby ]

# Use the database-backed adapters for Rails.cache, Active Job, and Action Cable
gem "solid_cache"
gem "solid_cable"

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false

# Deploy this application anywhere as a Docker container [https://kamal-deploy.org]
gem "kamal", require: false

# Add HTTP asset caching/compression and X-Sendfile acceleration to Puma [https://github.com/basecamp/thruster/]
gem "thruster", require: false

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
gem "image_processing", "~> 1.2"

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[ mri mingw mswin x64_mingw ], require: "debug/prelude"

  # Static analysis for security vulnerabilities [https://brakemanscanner.org/]
  gem "brakeman", require: false

  # Omakase Ruby styling [https://github.com/rails/rubocop-rails-omakase/]
  gem "rubocop-rails-omakase", require: false

  # Testing Framework
  gem "rspec-rails"
  gem "factory_bot_rails"
  gem "faker"
  gem "shoulda-matchers"
  gem "webmock"
  gem "vcr"

  # Testing utilities
  gem "database_cleaner-active_record"
  gem "simplecov", require: false
end

group :development do
  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem "web-console"

  # Development helpers
  gem "annotate"
  gem "letter_opener"
  gem "bullet" # N+1 query detection

  # Performance monitoring
  gem "rack-mini-profiler" # Performance profiling
  gem "memory_profiler" # Memory usage profiling
  gem "stackprof" # Sampling profiler
end

group :test do
  gem "capybara"
  gem "selenium-webdriver"
end
