# frozen_string_literal: true

# Ruby LLM Configuration for Enterprise AI Marketing Platform
# This configures multi-provider AI integration with production-grade settings

RubyLLM.configure do |config|
  # Multi-provider API keys from environment variables
  config.openai_api_key = ENV.fetch("OPENAI_API_KEY", nil)
  config.anthropic_api_key = ENV.fetch("ANTHROPIC_API_KEY", nil)
  config.gemini_api_key = ENV.fetch("GEMINI_API_KEY", nil)
  config.deepseek_api_key = ENV.fetch("DEEPSEEK_API_KEY", nil)
  config.openrouter_api_key = ENV.fetch("OPENROUTER_API_KEY", nil)

  # Performance optimization settings
  config.max_retries = 5
  config.retry_interval = 0.5
  config.retry_backoff_factor = 3
  config.request_timeout = 180 # 3 minutes for complex operations

  # Production logging configuration
  config.log_file = Rails.root.join("log", "ruby_llm.log")
  config.log_level = Rails.env.production? ? :warn : :info

  # Development mode enhancements
  if Rails.env.development?
    config.log_level = :debug
  end

  # Test mode configuration
  if Rails.env.test?
    config.log_level = :error
    config.request_timeout = 10
  end
end

# Enterprise provider routing strategies
module RubyLLMExtensions
  # Provider cost optimization mapping
  PROVIDER_COSTS = {
    "gpt-4o" => { cost_per_token: 0.00003, capabilities: [ :text, :vision, :function_calling ] },
    "gpt-4o-mini" => { cost_per_token: 0.0000015, capabilities: [ :text, :function_calling ] },
    "claude-3.5-sonnet" => { cost_per_token: 0.000015, capabilities: [ :text, :vision, :function_calling ] },
    "claude-3-opus" => { cost_per_token: 0.000075, capabilities: [ :text, :vision, :function_calling ] },
    "gemini-1.5-pro" => { cost_per_token: 0.0000035, capabilities: [ :text, :vision, :function_calling ] },
    "gemini-1.5-flash" => { cost_per_token: 0.********, capabilities: [ :text, :function_calling ] },
    "deepseek-chat" => { cost_per_token: 0.********, capabilities: [ :text, :function_calling ] }
  }.freeze

  # Task-based provider selection strategies
  PROVIDER_STRATEGIES = {
    creative_content: %w[gpt-4o claude-3.5-sonnet],
    data_analysis: %w[gemini-1.5-pro claude-3-opus],
    real_time_chat: %w[gpt-4o-mini gemini-1.5-flash],
    cost_sensitive: %w[deepseek-chat gemini-1.5-flash gpt-4o-mini],
    vision_tasks: %w[gpt-4o claude-3.5-sonnet gemini-1.5-pro],
    complex_reasoning: %w[claude-3-opus gpt-4o gemini-1.5-pro],
    function_calling: %w[gpt-4o claude-3.5-sonnet gemini-1.5-pro]
  }.freeze

  # Provider availability and health checking
  def self.check_provider_health(provider)
    case provider
    when "openai"
      ENV["OPENAI_API_KEY"].present?
    when "anthropic"
      ENV["ANTHROPIC_API_KEY"].present?
    when "gemini"
      ENV["GEMINI_API_KEY"].present?
    when "deepseek"
      ENV["DEEPSEEK_API_KEY"].present?
    when "openrouter"
      ENV["OPENROUTER_API_KEY"].present?
    else
      false
    end
  end

  # Get available providers for a task type
  def self.available_providers_for(task_type)
    providers = PROVIDER_STRATEGIES[task_type] || %w[gpt-4o-mini]
    providers.select { |provider| check_provider_health(provider.split("-").first) }
  end

  # Estimate cost for a task
  def self.estimate_cost(model, input_tokens, output_tokens = nil)
    cost_info = PROVIDER_COSTS[model]
    return 0.0 unless cost_info

    total_tokens = input_tokens + (output_tokens || input_tokens * 0.3) # Estimate 30% output ratio
    total_tokens * cost_info[:cost_per_token]
  end
end

# Make extensions available globally
Rails.application.config.after_initialize do
  unless Object.const_defined?("RubyLLMExtensions")
    Object.const_set("RubyLLMExtensions", RubyLLMExtensions)
  end
end
