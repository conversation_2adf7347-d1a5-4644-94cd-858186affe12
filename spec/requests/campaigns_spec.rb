require 'rails_helper'

RSpec.describe "Campaigns", type: :request do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  let(:campaign) { create(:campaign, tenant: tenant, created_by: user) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe "Authentication" do
    it "requires authentication for campaigns index" do
      get campaigns_path
      expect(response).to have_http_status(:redirect)
    end

    it "requires authentication for campaign show" do
      get campaign_path(campaign)
      expect(response).to have_http_status(:redirect)
    end

    it "requires authentication for new campaign" do
      get new_campaign_path
      expect(response).to have_http_status(:redirect)
    end
  end
end
