# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DashboardController, type: :controller do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }
  
  before do
    ActsAsTenant.current_tenant = tenant
    sign_in user
  end

  describe 'GET #index' do
    let!(:campaigns) { create_list(:campaign, 3, tenant: tenant, created_by: user) }
    let!(:vibe_records) { campaigns.map { |c| create(:vibe_analysis_record, campaign: c) } }
    let!(:emotional_profiles) { create_list(:emotional_resonance_profile, 3, tenant: tenant, created_by: user) }
    let!(:authenticity_checks) { campaigns.map { |c| create(:authenticity_check, campaign: c) } }

    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end

    it 'loads campaign statistics' do
      get :index
      expect(assigns(:campaign_stats)).to include(
        total: 3,
        active: kind_of(Integer),
        draft: kind_of(Integer),
        completed: kind_of(Integer),
        paused: kind_of(Integer)
      )
    end

    it 'calculates vibe metrics' do
      get :index
      expect(assigns(:vibe_metrics)).to include(
        :overall_vibe_score,
        :sentiment_distribution,
        :trending_vibes,
        :vibe_performance_trend,
        :total_analyzed
      )
    end

    it 'calculates emotional resonance metrics' do
      get :index
      expect(assigns(:emotional_resonance)).to include(
        :primary_emotion,
        :emotion_intensity,
        :emotion_distribution,
        :resonance_score,
        :engagement_correlation
      )
    end

    it 'calculates authenticity scores' do
      get :index
      expect(assigns(:authenticity_scores)).to include(
        :average_score,
        :flagged_campaigns,
        :approval_rate,
        :improvement_trend,
        :risk_assessment
      )
    end

    it 'calculates cultural alignment metrics' do
      get :index
      expect(assigns(:cultural_alignment)).to include(
        :alignment_score,
        :cultural_moments_captured,
        :trending_topics,
        :cultural_fit_rating,
        :regional_performance
      )
    end

    it 'loads recent campaigns' do
      get :index
      expect(assigns(:recent_campaigns)).to match_array(campaigns.reverse)
    end

    it 'loads budget statistics' do
      get :index
      expect(assigns(:budget_stats)).to include(
        :total_budget,
        :active_budget,
        :spent_budget,
        :remaining_budget
      )
    end

    it 'loads platform statistics' do
      get :index
      expect(assigns(:platform_stats)).to include(
        :email_campaigns,
        :social_campaigns,
        :multi_channel,
        :seo_campaigns
      )
    end

    it 'loads performance metrics' do
      get :index
      expect(assigns(:performance_metrics)).to include(
        :success_rate,
        :avg_roi,
        :engagement_rate,
        :conversion_rate,
        :cost_per_acquisition
      )
    end

    it 'loads dashboard settings' do
      get :index
      expect(assigns(:dashboard_settings)).to include(
        :layout_preference,
        :visible_widgets,
        :refresh_interval,
        :theme
      )
    end

    context 'when no campaigns exist' do
      let!(:campaigns) { [] }

      it 'returns default metrics' do
        get :index
        expect(assigns(:vibe_metrics)[:overall_vibe_score]).to eq(0.0)
        expect(assigns(:emotional_resonance)[:primary_emotion]).to eq("Neutral")
        expect(assigns(:authenticity_scores)[:average_score]).to eq(0.0)
        expect(assigns(:cultural_alignment)[:alignment_score]).to eq(0.0)
      end
    end

    context 'with different campaign statuses' do
      let!(:active_campaign) { create(:campaign, :active, tenant: tenant, created_by: user) }
      let!(:draft_campaign) { create(:campaign, :draft, tenant: tenant, created_by: user) }
      let!(:completed_campaign) { create(:campaign, :completed, tenant: tenant, created_by: user) }

      it 'correctly counts campaigns by status' do
        get :index
        stats = assigns(:campaign_stats)
        expect(stats[:total]).to eq(3)
        expect(stats[:active]).to eq(1)
        expect(stats[:draft]).to eq(1)
        expect(stats[:completed]).to eq(1)
      end
    end
  end

  describe 'authentication' do
    context 'when user is not signed in' do
      before { sign_out user }

      it 'redirects to sign in page' do
        get :index
        expect(response).to redirect_to(new_user_session_path)
      end
    end

    context 'when user has no tenant' do
      let(:user_without_tenant) { create(:user, tenant: nil) }
      
      before do
        sign_out user
        user_without_tenant.update!(tenant: nil)
        sign_in user_without_tenant
      end

      it 'redirects to root path with alert' do
        get :index
        expect(response).to redirect_to(root_path)
        expect(flash[:alert]).to eq("Please contact support to set up your account.")
      end
    end
  end

  describe 'tenant isolation' do
    let(:other_tenant) { create(:tenant) }
    let(:other_user) { create(:user, tenant: other_tenant) }
    let!(:other_campaigns) { create_list(:campaign, 2, tenant: other_tenant, created_by: other_user) }

    it 'only shows campaigns for current tenant' do
      get :index
      expect(assigns(:recent_campaigns)).not_to include(*other_campaigns)
      expect(assigns(:campaign_stats)[:total]).to eq(3) # Only campaigns from current tenant
    end
  end

  describe 'private methods' do
    describe '#calculate_vibe_metrics' do
      context 'with vibe analysis records' do
        let!(:campaign_with_vibe) { create(:campaign, tenant: tenant, created_by: user) }
        let!(:vibe_record) { create(:vibe_analysis_record, campaign: campaign_with_vibe, overall_score: 8.5) }

        it 'calculates metrics from database' do
          get :index
          metrics = assigns(:vibe_metrics)
          expect(metrics[:overall_vibe_score]).to be > 0
          expect(metrics[:total_analyzed]).to be > 0
        end
      end
    end

    describe '#calculate_emotional_resonance' do
      context 'with emotional resonance profiles' do
        let!(:campaign_with_emotion) { create(:campaign, tenant: tenant, created_by: user) }
        let!(:emotional_profile) { create(:emotional_resonance_profile, campaign: campaign_with_emotion) }

        it 'returns emotional resonance data' do
          get :index
          resonance = assigns(:emotional_resonance)
          expect(resonance[:primary_emotion]).to be_present
          expect(resonance[:emotion_intensity]).to be_a(Numeric)
          expect(resonance[:resonance_score]).to be_a(Numeric)
        end
      end
    end

    describe '#calculate_authenticity_scores' do
      context 'with authenticity checks' do
        let!(:campaign_with_auth) { create(:campaign, tenant: tenant, created_by: user) }
        let!(:auth_check) { create(:authenticity_check, campaign: campaign_with_auth, authenticity_score: 9.2) }

        it 'calculates authenticity metrics' do
          get :index
          scores = assigns(:authenticity_scores)
          expect(scores[:average_score]).to be > 0
          expect(scores[:approval_rate]).to be_a(Numeric)
          expect(scores[:risk_assessment]).to be_present
        end
      end
    end

    describe '#calculate_cultural_alignment' do
      it 'returns cultural alignment data' do
        get :index
        alignment = assigns(:cultural_alignment)
        expect(alignment[:alignment_score]).to be_a(Numeric)
        expect(alignment[:trending_topics]).to be_an(Array)
        expect(alignment[:cultural_fit_rating]).to be_present
      end
    end
  end
end