class CreateCampaignCollectionItems < ActiveRecord::Migration[8.0]
  def change
    create_table :campaign_collection_items do |t|
      t.references :campaign_collection, null: false, foreign_key: true
      t.references :campaign, null: false, foreign_key: true
      t.references :added_by, null: false, foreign_key: { to_table: :users }
      t.integer :sequence_order, default: 0, null: false
      t.jsonb :execution_conditions, default: {}, null: false
      t.jsonb :success_criteria, default: {}, null: false
      t.text :notes
      t.boolean :is_completed, default: false, null: false
      t.datetime :started_at
      t.datetime :completed_at

      t.timestamps
    end

    # Add unique constraint to prevent duplicate campaigns in same collection
    add_index :campaign_collection_items, [ :campaign_collection_id, :campaign_id ],
              unique: true, name: 'idx_campaign_collection_items_unique'

    # Add indexes for performance
    add_index :campaign_collection_items, :campaign_collection_id, name: 'idx_campaign_collection_items_collection'
    add_index :campaign_collection_items, :campaign_id, name: 'idx_campaign_collection_items_campaign'
    add_index :campaign_collection_items, :added_by_id, name: 'idx_campaign_collection_items_added_by'
    add_index :campaign_collection_items, :sequence_order, name: 'idx_campaign_collection_items_sequence'
    add_index :campaign_collection_items, :is_completed, name: 'idx_campaign_collection_items_completed'
  end
end
