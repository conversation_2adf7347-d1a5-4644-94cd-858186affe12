class CreateCampaignCollectionItems < ActiveRecord::Migration[8.0]
  def change
    create_table :campaign_collection_items do |t|
      t.references :campaign_collection, null: false, foreign_key: true
      t.references :campaign, null: false, foreign_key: true
      t.references :tenant, null: false, foreign_key: true
      t.references :added_by, null: false, foreign_key: { to_table: :users }
      t.integer :sequence_order, null: false
      t.text :notes
      t.text :execution_conditions
      t.text :success_criteria

      t.timestamps
    end

    # Add unique constraint to prevent duplicate campaigns in same collection
    add_index :campaign_collection_items, [:campaign_collection_id, :campaign_id],
              unique: true, name: 'idx_on_campaign_collection_id_campaign_id_985c92a9c4'

    # Add unique constraint for sequence order within collection
    add_index :campaign_collection_items, [:campaign_collection_id, :sequence_order],
              unique: true, name: 'idx_on_campaign_collection_id_sequence_order_6890334913'

    # Add indexes for performance
    add_index :campaign_collection_items, :campaign_collection_id
    add_index :campaign_collection_items, :campaign_id, name: 'idx_campaign_collection_items_campaign'
    add_index :campaign_collection_items, :tenant_id, name: 'idx_campaign_collection_items_tenant'
    add_index :campaign_collection_items, :added_by_id, name: 'idx_campaign_collection_items_added_by'
  end
end
