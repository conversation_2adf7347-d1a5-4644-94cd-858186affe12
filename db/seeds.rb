# frozen_string_literal: true

# Create seed data for AI Marketing Hub demo

puts "🌱 Seeding AI Marketing Hub..."

# Create demo tenant
demo_tenant = Tenant.find_or_create_by!(
  name: "Demo Corp",
  subdomain: "demo-corp"
) do |tenant|
  tenant.settings = {
    branding: {
      primary_color: "#3B82F6",
      logo_url: "https://via.placeholder.com/150x50/3B82F6/white?text=Demo+Corp"
    },
    features: {
      ai_enabled: true,
      email_campaigns: true,
      social_media: true,
      seo_tools: true
    }
  }
end

puts "✅ Created demo tenant: #{demo_tenant.name}"

# Create demo users
ActsAsTenant.with_tenant(demo_tenant) do
  # Create owner user
  owner_user = User.find_or_create_by!(email: "<EMAIL>") do |user|
    user.password = "password123"
    user.password_confirmation = "password123"
    user.first_name = "Sarah"
    user.last_name = "Johnson"
    user.role = "owner"
    user.confirmed_at = Time.current
    user.tenant = demo_tenant
  end

  # Create admin user
  admin_user = User.find_or_create_by!(email: "<EMAIL>") do |user|
    user.password = "password123"
    user.password_confirmation = "password123"
    user.first_name = "Mike"
    user.last_name = "<PERSON>"
    user.role = "admin"
    user.confirmed_at = Time.current
    user.tenant = demo_tenant
  end

  puts "✅ Created demo users: #{owner_user.full_name} (Owner), #{admin_user.full_name} (Admin)"

  # Create sample campaigns

  # 1. Active Email Campaign
  email_campaign = Campaign.create!(
    name: "Holiday Sale 2024",
    description: "Black Friday and Cyber Monday promotional campaign targeting existing customers",
    campaign_type: "email",
    status: "active",
    target_audience: "Existing customers who purchased in last 12 months",
    start_date: 1.week.ago,
    end_date: 1.week.from_now,
    budget_cents: 50000, # $500
    created_by: owner_user,
    tenant: demo_tenant,
    settings: {
      goals: {
        primary: "drive_sales",
        secondary: "increase_engagement",
        target_revenue: 25000
      }
    }
  )

  EmailCampaign.create!(
    campaign: email_campaign,
    subject_line: "🎉 Exclusive Holiday Sale - Up to 50% Off Everything!",
    preview_text: "Don't miss our biggest sale of the year. Limited time only!",
    content: "Hi {{first_name}},\n\nOur biggest sale of the year is here! Get up to 50% off everything in our store.\n\nUse code HOLIDAY50 at checkout.\n\nSale ends December 2nd - don't wait!\n\nShop now: [Shop Button]",
    from_name: "Demo Corp Team",
    from_email: "<EMAIL>",
    settings: {
      delivery_options: {
        send_immediately: false,
        scheduled_at: 2.hours.from_now.iso8601
      },
      tracking: {
        opens: true,
        clicks: true,
        unsubscribes: true
      },
      recipient_count: 12500
    }
  )

  # 2. Active Social Campaign
  social_campaign = Campaign.create!(
    name: "Brand Awareness Q4",
    description: "Multi-platform social media campaign to increase brand awareness and engagement",
    campaign_type: "social",
    status: "active",
    target_audience: "Tech-savvy professionals aged 25-45",
    start_date: 2.weeks.ago,
    end_date: 2.weeks.from_now,
    budget_cents: 30000, # $300
    created_by: admin_user,
    tenant: demo_tenant,
    settings: {
      goals: {
        primary: "increase_brand_awareness",
        target_impressions: 100000,
        target_engagement_rate: 0.05
      }
    }
  )

  SocialCampaign.create!(
    campaign: social_campaign,
    platforms: [ "twitter", "linkedin", "facebook" ],
    content_variants: {
      "twitter" => "🚀 Exciting news! Our AI marketing platform is helping SMBs compete with enterprise tools. Join the revolution! #MarketingAI #SMB",
      "linkedin" => "We're proud to announce that our AI marketing automation platform is now helping small and medium businesses access enterprise-level marketing tools. See how we're democratizing advanced marketing technology.",
      "facebook" => "Great news for small business owners! 🎉 Our new AI marketing platform makes professional campaign management accessible and affordable. Check out how we're changing the game!"
    },
    hashtags: "#MarketingAI #SMB #Automation #BusinessGrowth",
    target_demographics: {
      age_range: "25-45",
      interests: [ "business", "technology", "marketing", "entrepreneurship" ],
      location: [ "United States", "Canada", "United Kingdom" ]
    },
    social_settings: {
      follower_counts: {
        twitter: 8500,
        linkedin: 5200,
        facebook: 12000
      },
      engagement_rate: 0.08
    }
  )

  # 3. Draft Multi-channel Campaign
  Campaign.create!(
    name: "New Product Launch",
    description: "Comprehensive launch campaign for our new AI analytics feature",
    campaign_type: "multi_channel",
    status: "draft",
    target_audience: "Current users and prospects interested in analytics",
    start_date: 1.month.from_now,
    end_date: 2.months.from_now,
    budget_cents: 100000, # $1000
    created_by: owner_user,
    tenant: demo_tenant,
    settings: {
      goals: {
        primary: "product_launch",
        target_signups: 500,
        target_revenue: 50000
      }
    }
  )

  # 4. Completed Campaign
  Campaign.create!(
    name: "Customer Onboarding Series",
    description: "Welcome email series for new customers",
    campaign_type: "email",
    status: "completed",
    target_audience: "New customers in first 30 days",
    start_date: 2.months.ago,
    end_date: 1.month.ago,
    budget_cents: 15000, # $150
    created_by: admin_user,
    tenant: demo_tenant,
    settings: {
      goals: {
        primary: "improve_onboarding",
        target_completion_rate: 0.75
      }
    }
  )

  # 5. Paused Campaign
  Campaign.create!(
    name: "Summer Promotion",
    description: "Summer sale campaign - paused due to inventory issues",
    campaign_type: "social",
    status: "paused",
    target_audience: "All customers and prospects",
    start_date: 1.month.ago,
    end_date: 2.weeks.from_now,
    budget_cents: 40000, # $400
    created_by: owner_user,
    tenant: demo_tenant,
    settings: {
      goals: {
        primary: "drive_summer_sales",
        target_revenue: 30000
      }
    }
  )

  puts "✅ Created 5 sample campaigns with various statuses"
end

puts "🎉 Seeding completed! Demo data ready."
puts ""
puts "Demo login credentials:"
puts "Owner: <EMAIL> / password123"
puts "Admin: <EMAIL> / password123"
puts ""
puts "Visit: http://localhost:3000"
